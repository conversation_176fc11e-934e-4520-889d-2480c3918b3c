import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/features/members/presentation/screens/create_member/members_form.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:logger/logger.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart' hide Border;

import '../../../../../core/app/utils/show_toast.dart';
import '../../../../../core/app/constants/routes.dart';
import '../../../../../data/models/member_model.dart';
import '../../../controllers/member_controller.dart';
import '../../../../auth/controllers/auth_controller.dart';
import '../../../../../core/app/widgets/right_sidebar.dart';
import '../../../models/member_payload_model.dart';

class AddMultipleMembersScreen extends StatefulWidget {
  const AddMultipleMembersScreen({super.key});

  @override
  State<AddMultipleMembersScreen> createState() =>
      _AddMultipleMembersScreenState();
}

class _AddMultipleMembersScreenState extends State<AddMultipleMembersScreen> {
  final memberController = Get.find<MemberController>();
  final authController = Get.find<AuthController>();
  final logger = Get.find<Logger>();

  final RxList<MemberModel> membersToAdd = <MemberModel>[].obs;
  final RxList<MemberModel> missedMembers = <MemberModel>[].obs;
  final RxMap<String, List<String>> memberErrors = <String, List<String>>{}.obs;
  final RxBool isLoading = false.obs;
  final RxBool hasSubmissionResponse = false.obs;

  late List<PlutoColumn> columns;
  late PlutoGridStateManager stateManager;

  @override
  void initState() {
    super.initState();
    setColumns();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void setColumns() {
    columns = [
      PlutoColumn(
        title: 'First Name',
        field: 'first_name',
        type: PlutoColumnType.text(),
        width: 130,
        minWidth: 100,
        frozen: PlutoColumnFrozen.none,
      ),
      PlutoColumn(
        title: 'Last Name',
        field: 'second_name',
        type: PlutoColumnType.text(),
        width: 130,
        minWidth: 100,
        frozen: PlutoColumnFrozen.none,
      ),
      PlutoColumn(
        title: 'Phone',
        field: 'phone',
        type: PlutoColumnType.text(),
        width: 140,
        minWidth: 120,
        frozen: PlutoColumnFrozen.none,
      ),
      PlutoColumn(
        title: 'Email',
        field: 'email',
        type: PlutoColumnType.text(),
        width: 150,
        minWidth: 120,
        frozen: PlutoColumnFrozen.none,
      ),
      PlutoColumn(
        title: 'ID Number',
        field: 'id_number',
        type: PlutoColumnType.text(),
        width: 100,
        minWidth: 80,
        frozen: PlutoColumnFrozen.none,
      ),
      PlutoColumn(
        title: 'Gender',
        field: 'gender',
        type: PlutoColumnType.text(),
        width: 80,
        minWidth: 60,
        frozen: PlutoColumnFrozen.none,
      ),
      PlutoColumn(
        title: 'Date of Birth',
        field: 'dob',
        type: PlutoColumnType.text(),
        width: 100,
        minWidth: 80,
        frozen: PlutoColumnFrozen.none,
      ),
      PlutoColumn(
        title: 'Category',
        field: 'category',
        type: PlutoColumnType.text(),
        width: 140,
        minWidth: 100,
        frozen: PlutoColumnFrozen.none,
      ),
      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 150,
        minWidth: 120,
        frozen: PlutoColumnFrozen.none,
        renderer: (rendererContext) {
          final status = rendererContext.cell.value as String;
          if (status.isEmpty) {
            return const SizedBox.shrink();
          }

          final isError =
              status.toLowerCase().contains('failed') ||
              status.toLowerCase().contains('error') ||
              status.toLowerCase().contains('exists');

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color:
                  isError
                      ? Colors.red.withOpacity(0.1)
                      : Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: isError ? Colors.red : Colors.green,
                width: 1,
              ),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: isError ? Colors.red : Colors.green,
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 100,
        minWidth: 80,
        frozen: PlutoColumnFrozen.none,
        renderer: (rendererContext) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: const Icon(Icons.edit, size: 20),
                onPressed: () => _editMember(rendererContext.rowIdx),
                tooltip: 'Edit',
                color: Colors.blue,
              ),
              IconButton(
                icon: const Icon(Icons.delete, size: 20),
                onPressed: () => _removeMember(rendererContext.rowIdx),
                tooltip: 'Remove',
                color: Colors.red,
              ),
            ],
          );
        },
      ),
    ];
  }

  List<PlutoRow> _getRows() {
    final allMembers = [...membersToAdd, ...missedMembers];

    return allMembers.map((member) {
      // Format date of birth if available
      String formattedDob = '';
      if (member.dob != null) {
        formattedDob =
            '${member.dob!.day}/${member.dob!.month}/${member.dob!.year}';
      }

      // Get status for this member
      String status = '';
      if (missedMembers.contains(member)) {
        // This is a missed member, get the error message
        final memberKey =
            '${member.phoneNumber}_${member.firstName}_${member.secondName}';
        final errors = memberErrors[memberKey];
        if (errors != null && errors.isNotEmpty) {
          status = errors.join(', ');
        } else {
          status = 'Failed';
        }
      }

      return PlutoRow(
        cells: {
          'first_name': PlutoCell(value: member.firstName ?? ''),
          'second_name': PlutoCell(value: member.secondName ?? ''),
          'phone': PlutoCell(value: member.phoneNumber ?? ''),
          'email': PlutoCell(value: member.email ?? ''),
          'id_number': PlutoCell(value: member.idNumber ?? ''),
          'gender': PlutoCell(value: member.gender ?? ''),
          'dob': PlutoCell(value: formattedDob),
          'category': PlutoCell(value: member.memberCategory?.title ?? ''),
          'status': PlutoCell(value: status),
          'actions': PlutoCell(value: ''),
        },
      );
    }).toList();
  }

  void _addNewMember() {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    if (isSmallScreen) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder:
            (context) => MemberFormBottomSheet(
              onSave: (member) {
                // Clean phone number before adding
                final cleanedMember = member.copyWith(
                  phoneNumber: member.phoneNumber?.replaceAll('+', ''),
                );
                membersToAdd.add(cleanedMember);
                stateManager.appendRows(
                  _getRows().sublist(membersToAdd.length - 1),
                );
                Navigator.pop(context);
              },
            ),
      );
    } else {
      // Show right sidebar using the new implementation
      RightSidebar.show(
        context: context,
        title: 'Add New Member',
        child: MemberFormDialog(
          onSave: (member) {
            // Clean phone number before adding
            final cleanedMember = member.copyWith(
              phoneNumber: member.phoneNumber?.replaceAll('+', ''),
            );
            membersToAdd.add(cleanedMember);
            stateManager.appendRows(
              _getRows().sublist(membersToAdd.length - 1),
            );
            Navigator.of(context).pop(); // Close the sidebar
          },
        ),
        onClose: () {
          // This will be called when the sidebar is closed
        },
      );
    }
  }

  void _editMember(int rowIndex) {
    final allMembers = [...membersToAdd, ...missedMembers];
    final member = allMembers[rowIndex];
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    if (isSmallScreen) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder:
            (context) => MemberFormBottomSheet(
              member: member,
              onSave: (updatedMember) {
                _updateMemberInLists(rowIndex, updatedMember);
                Navigator.pop(context);
              },
            ),
      );
    } else {
      // Show right sidebar using the new implementation
      RightSidebar.show(
        context: context,
        title: 'Edit Member',
        child: MemberFormDialog(
          member: member,
          onSave: (updatedMember) {
            _updateMemberInLists(rowIndex, updatedMember);
            Navigator.of(context).pop(); // Close the sidebar
          },
        ),
        onClose: () {
          // This will be called when the sidebar is closed
        },
      );
    }
  }

  void _updateMemberInLists(int rowIndex, MemberModel updatedMember) {
    final allMembers = [...membersToAdd, ...missedMembers];
    final originalMember = allMembers[rowIndex];

    // Clean phone number before updating
    final cleanedMember = updatedMember.copyWith(
      phoneNumber: updatedMember.phoneNumber?.replaceAll('+', ''),
    );

    // Determine which list the member belongs to
    final membersToAddIndex = membersToAdd.indexOf(originalMember);
    final missedMembersIndex = missedMembers.indexOf(originalMember);

    if (membersToAddIndex != -1) {
      membersToAdd[membersToAddIndex] = cleanedMember;
    } else if (missedMembersIndex != -1) {
      missedMembers[missedMembersIndex] = cleanedMember;
      // Clear the error for this member since it's been edited
      final memberKey =
          '${cleanedMember.phoneNumber}_${cleanedMember.firstName}_${cleanedMember.secondName}';
      memberErrors.remove(memberKey);
    }

    // Refresh the grid
    stateManager.removeRows([stateManager.rows[rowIndex]]);
    stateManager.insertRows(
      rowIndex,
      _getRows().sublist(rowIndex, rowIndex + 1),
    );
  }

  void _removeMember(int rowIndex) {
    final allMembers = [...membersToAdd, ...missedMembers];
    final memberToRemove = allMembers[rowIndex];

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Remove Member'),
            content: const Text('Are you sure you want to remove this member?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              FilledButton(
                onPressed: () {
                  // Remove from appropriate list
                  final membersToAddIndex = membersToAdd.indexOf(
                    memberToRemove,
                  );
                  final missedMembersIndex = missedMembers.indexOf(
                    memberToRemove,
                  );

                  if (membersToAddIndex != -1) {
                    membersToAdd.removeAt(membersToAddIndex);
                  } else if (missedMembersIndex != -1) {
                    missedMembers.removeAt(missedMembersIndex);
                    // Also remove the error
                    final memberKey =
                        '${memberToRemove.phoneNumber}_${memberToRemove.firstName}_${memberToRemove.secondName}';
                    memberErrors.remove(memberKey);
                  }

                  stateManager.removeRows([stateManager.rows[rowIndex]]);
                  Navigator.pop(context);
                },
                child: const Text('Remove'),
              ),
            ],
          ),
    );
  }

  Future<void> _uploadExcelFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result != null) {
        isLoading.value = true;

        final bytes = result.files.first.bytes;
        if (bytes == null) {
          ToastUtils.showErrorToast('Failed to read file', null);
          return;
        }

        final excel = Excel.decodeBytes(bytes);
        final sheet = excel.tables.keys.first;
        final rows = excel.tables[sheet]!.rows;

        if (rows.length <= 1) {
          ToastUtils.showErrorToast('Excel file has no data rows', null);
          return;
        }

        // Skip header row
        for (int i = 1; i < rows.length; i++) {
          final row = rows[i];
          if (row.isEmpty) continue;

          final member = MemberModel(
            firstName: _getCellValue(row, 0),
            secondName: _getCellValue(row, 1),
            phoneNumber: _getCellValue(row, 2).replaceAll('+', ''),
            email: _getCellValue(row, 3),
            idNumber: _getCellValue(row, 4),
            address: _getCellValue(row, 5),
            joinDate: _parseDate(_getCellValue(row, 6)),
            dob: _parseDate(_getCellValue(row, 7)),
            gender: _getCellValue(row, 8),
            maritalStatus: _getCellValue(row, 9),
            occupation: _getCellValue(row, 10),
            educationLevel: _getCellValue(row, 11),
            nationality: _getCellValue(row, 12),
            organisationId: authController.currentOrg.value?.id.toString(),
          );

          membersToAdd.add(member);
        }

        stateManager.removeAllRows();
        stateManager.appendRows(_getRows());

        ToastUtils.showSuccessToast(
          'Successfully imported ${membersToAdd.length} members',
          null,
        );
      }
    } catch (e) {
      logger.e('Error uploading Excel file: $e');
      ToastUtils.showErrorToast('Error uploading Excel file: $e', null);
    } finally {
      isLoading.value = false;
    }
  }

  String _getCellValue(List<dynamic> row, int index) {
    if (index >= row.length) return '';
    return row[index]?.value?.toString() ?? '';
  }

  DateTime? _parseDate(String dateStr) {
    if (dateStr.isEmpty) return null;
    try {
      return DateTime.parse(dateStr);
    } catch (e) {
      return null;
    }
  }

  /// Convert MemberModel list to MemberPayloadModel list for API submission
  List<MemberPayloadModel> _convertToPayloads(List<MemberModel> members) {
    return members.map((member) {
      // Clean phone number by removing plus sign before conversion
      final cleanedMember = member.copyWith(
        phoneNumber: member.phoneNumber?.replaceAll('+', ''),
      );

      return MemberPayloadModel.fromMemberModel(
        cleanedMember,
        authController.currentOrg.value?.id ?? '',
      );
    }).toList();
  }

  /// Handle missed members from API response
  void _handleMissedMembers(List<dynamic> failedMembersData) {
    // Clear previous missed members and errors
    missedMembers.clear();
    memberErrors.clear();

    // Clear successfully added members from the main list
    membersToAdd.clear();

    for (final memberData in failedMembersData) {
      try {
        // Parse the member data
        final member = MemberModel.fromJson(memberData);
        missedMembers.add(member);

        // Extract error messages
        final memberKey =
            '${member.phoneNumber}_${member.firstName}_${member.secondName}';
        final onboardingResponse =
            memberData['onboarding_response'] as List<dynamic>?;

        if (onboardingResponse != null && onboardingResponse.isNotEmpty) {
          memberErrors[memberKey] = onboardingResponse.cast<String>();
        } else {
          memberErrors[memberKey] = ['Failed to add member'];
        }
      } catch (e) {
        logger.e('Error parsing missed member: $e');
      }
    }

    // Refresh the grid to show missed members with errors
    stateManager.removeAllRows();
    stateManager.appendRows(_getRows());
  }

  Future<void> _submitMembers() async {
    if (membersToAdd.isEmpty && missedMembers.isEmpty) {
      ToastUtils.showErrorToast('No members to add', null);
      return;
    }

    try {
      isLoading.value = true;

      // Convert MemberModel list to MemberPayloadModel list using helper method
      final List<MemberPayloadModel> memberPayloads = _convertToPayloads([
        ...membersToAdd,
        ...missedMembers,
      ]);

      final response = await memberController.registerMembers(memberPayloads);

      if (response['status'] == true) {
        final data = response['data'];
        final failedMembers = data?['missed_members'] as List<dynamic>?;

        if (failedMembers != null && failedMembers.isNotEmpty) {
          // Handle missed members
          _handleMissedMembers(failedMembers);
          hasSubmissionResponse.value = true;

          ToastUtils.showErrorToast(
            '${failedMembers.length} members failed to be added. Please review and fix the errors.',
            null,
          );
        } else {
          // All members were successfully added
          ToastUtils.showSuccessToast('Successfully added all members', null);
          membersToAdd.clear();
          missedMembers.clear();
          memberErrors.clear();
          stateManager.removeAllRows();
          hasSubmissionResponse.value = false;

          // Navigate back to members list
          context.go(Routes.MEMBERS);
        }
      } else {
        ToastUtils.showErrorToast(
          response['message'] ?? 'Failed to register members',
          null,
        );
      }
    } catch (e) {
      logger.e('Error submitting members: $e');
      ToastUtils.showErrorToast('Error submitting members: $e', null);
    } finally {
      isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Multiple Members'),
        elevation: 0,
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        actions: [
          IconButton(
            icon: const Icon(Icons.upload_file),
            onPressed: _uploadExcelFile,
            tooltip: 'Upload Excel',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => context.go(Routes.MEMBER_IMPORT_HELPER),
            tooltip: 'Import Help',
          ),
        ],
      ),
      body: Obx(
        () => Stack(
          children: [
            Container(
              color: colorScheme.surface,
              child: Column(
                children: [
                  // Header and controls
                  Container(
                    padding: EdgeInsets.all(16.r),
                    decoration: BoxDecoration(
                      color: colorScheme.surface,
                      boxShadow: [
                        BoxShadow(
                          color: colorScheme.shadow.withOpacity(0.05),
                          blurRadius: 2,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Obx(() {
                          final totalMembers =
                              membersToAdd.length + missedMembers.length;
                          final hasErrors = missedMembers.isNotEmpty;

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                hasErrors
                                    ? 'Total Members: $totalMembers (${missedMembers.length} failed)'
                                    : 'Members to Add: ${membersToAdd.length}',
                                style: theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color:
                                      hasErrors
                                          ? Colors.orange
                                          : colorScheme.primary,
                                ),
                              ),
                              if (hasErrors)
                                Text(
                                  'Please fix the errors and resubmit',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: Colors.red,
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                            ],
                          );
                        }),
                        const Spacer(),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.add),
                          label: const Text('Add Member'),
                          onPressed: _addNewMember,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: colorScheme.primary,
                            foregroundColor: colorScheme.onPrimary,
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: EdgeInsets.symmetric(
                              horizontal: 16.r,
                              vertical: 12.r,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Members grid
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      margin: EdgeInsets.symmetric(
                        horizontal: 8.r,
                        vertical: 4.r,
                      ),
                      decoration: BoxDecoration(
                        color: colorScheme.surface,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: SizedBox(
                          width: double.infinity,
                          height: double.infinity,
                          child: PlutoGrid(
                            columns: columns,
                            rows: _getRows(),
                            onLoaded: (PlutoGridOnLoadedEvent event) {
                              stateManager = event.stateManager;
                              // Enable column filter
                              stateManager.setShowColumnFilter(true);
                              // Force grid to stretch and fill available space
                              Future.microtask(() {
                                stateManager.notifyListeners();
                              });
                            },
                            mode: PlutoGridMode.normal,
                            configuration: PlutoGridConfiguration(
                              style: PlutoGridStyleConfig(
                                activatedColor: colorScheme.primary.withOpacity(
                                  0.1,
                                ),
                                cellTextStyle: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: colorScheme.onSurface,
                                ),
                                columnTextStyle: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: colorScheme.primary,
                                ),
                                gridBackgroundColor: colorScheme.surface,
                                borderColor: colorScheme.outline.withOpacity(
                                  0.2,
                                ),
                              ),
                              columnSize: const PlutoGridColumnSizeConfig(
                                autoSizeMode: PlutoAutoSizeMode.scale,
                                resizeMode: PlutoResizeMode.normal,
                                restoreAutoSizeAfterHideColumn: true,
                                restoreAutoSizeAfterFrozenColumn: true,
                                restoreAutoSizeAfterMoveColumn: true,
                              ),
                              scrollbar: const PlutoGridScrollbarConfig(
                                isAlwaysShown: false,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Submit button
                  Container(
                    padding: EdgeInsets.all(16.r),
                    decoration: BoxDecoration(
                      color: colorScheme.surface,
                      boxShadow: [
                        BoxShadow(
                          color: colorScheme.shadow.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, -2),
                        ),
                      ],
                    ),
                    child: SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: Obx(() {
                        final hasMembers =
                            membersToAdd.isNotEmpty || missedMembers.isNotEmpty;
                        final hasErrors = missedMembers.isNotEmpty;

                        return ElevatedButton.icon(
                          icon: Icon(hasErrors ? Icons.refresh : Icons.save),
                          label: Text(
                            hasErrors
                                ? 'RETRY FAILED MEMBERS'
                                : 'SUBMIT ALL MEMBERS',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          onPressed: hasMembers ? _submitMembers : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: colorScheme.primary,
                            foregroundColor: colorScheme.onPrimary,
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        );
                      }),
                    ),
                  ),
                ],
              ),
            ),

            // Loading overlay
            if (isLoading.value)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: Center(
                  child: CircularProgressIndicator(color: colorScheme.primary),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
