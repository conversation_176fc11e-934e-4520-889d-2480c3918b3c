import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/create_organization_controller.dart';
import 'color_picker_widget.dart';

class OrganizationSettingsWidget extends StatelessWidget {
  const OrganizationSettingsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CreateOrganizationController>();
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Settings Header
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.primary.withOpacity(0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.settings,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Organization Settings',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Configure theme colors and other organization-specific settings.',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),

        // Theme Colors Section
        Text(
          'Theme Colors',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),

        // Primary Color
        Obx(() => ColorPickerWidget(
          label: 'Primary Color',
          selectedColor: controller.primaryColor.value,
          onColorSelected: (color) => controller.setPrimaryColor(color),
          errorText: controller.settingsErrors['primaryColor'],
        )),
        const SizedBox(height: 16),

        // Secondary Color
        Obx(() => ColorPickerWidget(
          label: 'Secondary Color',
          selectedColor: controller.secondaryColor.value,
          onColorSelected: (color) => controller.setSecondaryColor(color),
          errorText: controller.settingsErrors['secondaryColor'],
        )),
        const SizedBox(height: 16),

        // Accent Color
        Obx(() => ColorPickerWidget(
          label: 'Accent Color',
          selectedColor: controller.accentColor.value,
          onColorSelected: (color) => controller.setAccentColor(color),
          errorText: controller.settingsErrors['accentColor'],
        )),
        const SizedBox(height: 16),

        // Background Color
        Obx(() => ColorPickerWidget(
          label: 'Background Color',
          selectedColor: controller.backgroundColor.value,
          onColorSelected: (color) => controller.setBackgroundColor(color),
          errorText: controller.settingsErrors['backgroundColor'],
        )),
        const SizedBox(height: 24),

        // Preview Section
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.preview,
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Color Preview',
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              
              Obx(() => _buildColorPreview(context, controller)),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // Reset to Defaults Button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => controller.resetSettingsToDefaults(),
            icon: const Icon(Icons.refresh),
            label: const Text('Reset to Default Colors'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildColorPreview(BuildContext context, CreateOrganizationController controller) {
    return Container(
      width: double.infinity,
      height: 120,
      decoration: BoxDecoration(
        color: controller.backgroundColor.value ?? Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Primary color sample
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: controller.primaryColor.value ?? Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'Primary',
                style: TextStyle(
                  color: _getContrastColor(controller.primaryColor.value ?? Theme.of(context).colorScheme.primary),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const SizedBox(height: 8),
            
            // Secondary and accent colors row
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: controller.secondaryColor.value ?? Colors.grey,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Secondary',
                    style: TextStyle(
                      color: _getContrastColor(controller.secondaryColor.value ?? Colors.grey),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: controller.accentColor.value ?? Colors.blue,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Accent',
                    style: TextStyle(
                      color: _getContrastColor(controller.accentColor.value ?? Colors.blue),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            Text(
              'This is how your organization colors will look',
              style: TextStyle(
                color: _getContrastColor(controller.backgroundColor.value ?? Colors.white),
                fontSize: 11,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}
