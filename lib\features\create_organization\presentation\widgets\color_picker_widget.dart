import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ColorPickerWidget extends StatelessWidget {
  final String label;
  final Color? selectedColor;
  final Function(Color) onColorSelected;
  final String? errorText;

  const ColorPickerWidget({
    super.key,
    required this.label,
    this.selectedColor,
    required this.onColorSelected,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface.withOpacity(0.7),
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),

        InkWell(
          onTap: () => _showColorPicker(context),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: double.infinity,
            height: 56,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color:
                    errorText != null
                        ? colorScheme.error
                        : colorScheme.outline.withOpacity(0.5),
                width: errorText != null ? 2 : 1,
              ),
              color: colorScheme.surfaceVariant.withOpacity(0.25),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  Icon(
                    Icons.palette,
                    color: colorScheme.primary.withOpacity(0.8),
                    size: 20,
                  ),
                  const SizedBox(width: 12),

                  // Color preview
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: selectedColor ?? Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: colorScheme.outline.withOpacity(0.5),
                        width: 1,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  Expanded(
                    child: Text(
                      selectedColor != null
                          ? '#${selectedColor!.value.toRadixString(16).substring(2).toUpperCase()}'
                          : 'Select color',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color:
                            selectedColor != null
                                ? colorScheme.onSurface
                                : colorScheme.onSurface.withOpacity(0.5),
                      ),
                    ),
                  ),

                  Icon(
                    Icons.arrow_drop_down,
                    color: colorScheme.onSurface.withOpacity(0.6),
                  ),
                ],
              ),
            ),
          ),
        ),

        if (errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  void _showColorPicker(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Select $label'),
            content: SizedBox(
              width: 300,
              height: 400,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // Predefined colors grid
                    _buildPredefinedColors(context),
                    const SizedBox(height: 16),

                    // Custom color input
                    _buildCustomColorInput(context),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
            ],
          ),
    );
  }

  Widget _buildPredefinedColors(BuildContext context) {
    final predefinedColors = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.deepPurple,
      Colors.indigo,
      Colors.blue,
      Colors.lightBlue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lightGreen,
      Colors.lime,
      Colors.yellow,
      Colors.amber,
      Colors.orange,
      Colors.deepOrange,
      Colors.brown,
      Colors.grey,
      Colors.blueGrey,
      Colors.black,
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Predefined Colors',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 5,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemCount: predefinedColors.length,
          itemBuilder: (context, index) {
            final color = predefinedColors[index];
            final isSelected = selectedColor?.value == color.value;

            return InkWell(
              onTap: () {
                onColorSelected(color);
                Navigator.of(context).pop();
              },
              borderRadius: BorderRadius.circular(8),
              child: Container(
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color:
                        isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Colors.grey.shade300,
                    width: isSelected ? 3 : 1,
                  ),
                ),
                child:
                    isSelected
                        ? Icon(
                          Icons.check,
                          color: _getContrastColor(color),
                          size: 20,
                        )
                        : null,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildCustomColorInput(BuildContext context) {
    final TextEditingController hexController = TextEditingController();

    if (selectedColor != null) {
      hexController.text =
          selectedColor!.value.toRadixString(16).substring(2).toUpperCase();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Custom Color (Hex)',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: hexController,
          decoration: InputDecoration(
            hintText: 'Enter hex color (e.g., FF5722)',
            prefixText: '#',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            suffixIcon: IconButton(
              icon: const Icon(Icons.check),
              onPressed: () {
                final hexValue = hexController.text.trim();
                if (hexValue.isNotEmpty) {
                  try {
                    final color = Color(int.parse('FF$hexValue', radix: 16));
                    onColorSelected(color);
                    Navigator.of(context).pop();
                  } catch (e) {
                    Get.snackbar(
                      'Invalid Color',
                      'Please enter a valid hex color code',
                      snackPosition: SnackPosition.bottom,
                    );
                  }
                }
              },
            ),
          ),
          maxLength: 6,
          textCapitalization: TextCapitalization.characters,
        ),
      ],
    );
  }

  Color _getContrastColor(Color color) {
    // Calculate luminance to determine if we should use black or white text
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}
