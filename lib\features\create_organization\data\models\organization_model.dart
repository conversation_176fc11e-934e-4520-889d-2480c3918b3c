class OrganizationModel {
  final String? id;
  final String name;
  final String description;
  final String registrationType;
  final String email;
  final String phoneNumber;
  final String? businessNumber;
  final String? kraPin;
  final String? logo;
  final String? referralCode;
  final String slogan;
  final String? superOrganisationId;
  final List<dynamic>? location;
  final Map<String, dynamic>? settings;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  OrganizationModel({
    this.id,
    required this.name,
    required this.description,
    required this.registrationType,
    required this.email,
    required this.phoneNumber,
    this.businessNumber,
    this.kraPin,
    this.logo,
    this.referralCode,
    required this.slogan,
    this.superOrganisationId,
    this.location,
    this.settings,
    this.createdAt,
    this.updatedAt,
  });

  factory OrganizationModel.fromJson(Map<String, dynamic> json) {
    return OrganizationModel(
      id: json['id'],
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      registrationType: json['registration_type'] ?? '',
      email: json['email'] ?? '',
      phoneNumber: json['phone_number'] ?? '',
      businessNumber: json['business_number'],
      kraPin: json['kra_pin'],
      logo: json['logo'],
      referralCode: json['referral_code'],
      slogan: json['slogan'] ?? '',
      superOrganisationId: json['super_organisation_id'],
      location: json['location'],
      settings:
          json['settings'] != null
              ? Map<String, dynamic>.from(json['settings'])
              : null,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'description': description,
      'registration_type': registrationType,
      'email': email,
      'phone_number': phoneNumber,
      if (businessNumber != null) 'business_number': businessNumber,
      if (kraPin != null) 'kra_pin': kraPin,
      if (logo != null) 'logo': logo,
      if (referralCode != null) 'referral_code': referralCode,
      'slogan': slogan,
      if (superOrganisationId != null)
        'super_organisation_id': superOrganisationId,
      if (location != null) 'location': location,
      if (settings != null) 'settings': settings,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }

  OrganizationModel copyWith({
    String? id,
    String? name,
    String? description,
    String? registrationType,
    String? email,
    String? phoneNumber,
    String? businessNumber,
    String? kraPin,
    String? logo,
    String? referralCode,
    String? slogan,
    String? superOrganisationId,
    List<dynamic>? location,
    Map<String, dynamic>? settings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OrganizationModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      registrationType: registrationType ?? this.registrationType,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      businessNumber: businessNumber ?? this.businessNumber,
      kraPin: kraPin ?? this.kraPin,
      logo: logo ?? this.logo,
      referralCode: referralCode ?? this.referralCode,
      slogan: slogan ?? this.slogan,
      superOrganisationId: superOrganisationId ?? this.superOrganisationId,
      location: location ?? this.location,
      settings: settings ?? this.settings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OrganizationModel &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          email == other.email;

  @override
  int get hashCode => Object.hash(id, name, email);

  @override
  String toString() {
    return 'OrganizationModel{id: $id, name: $name, email: $email, registrationType: $registrationType}';
  }
}
