name: onechurch
description: "OneChurch is a Church Management System (ChMS) designed to help churches streamline operations, improve communication, and enhance member engagement through a centralized digital platform."
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  cached_network_image: ^3.4.1
  currency_formatter: ^2.2.3
  get: ^5.0.0-release-candidate-9.3.2
  go_router: ^15.1.1
  get_storage: ^2.1.1
  video_player: ^2.9.5
  flutter_secure_storage: ^9.2.4
  flutter_iconly: ^1.0.2
  flutter_admin_scaffold: ^1.4.0
  flutter_quill: ^11.4.0
  firebase_core:
  dio: ^5.8.0+1
  dynamic_path_url_strategy: ^1.0.0
  flutter_screenutil: ^5.9.3
  device_preview_plus: ^2.3.3
  firebase_auth: ^5.5.1
  logger: ^2.5.0
  loading_animation_widget: ^1.3.0
  firebase_cloud_messaging_flutter: ^1.0.6
  flutter_local_notifications: ^19.0.0
  intl_phone_number_input: ^0.7.4
  geolocator: ^10.1.0
  permission_handler_html: ^0.1.0
  gap: ^3.0.1
  firebase_messaging: ^15.2.5
  google_sign_in: ^6.2.1
  pin_code_fields: ^8.0.1
  pluto_grid: ^8.0.0
  intl: ^0.20.2
  flutter_html: ^3.0.0
  share_plus: ^7.2.1
  toastification: ^3.0.2
  url_launcher: ^6.2.5
  image_picker: ^1.1.2
  skeletonizer: ^2.0.1
  connectivity_plus: ^6.1.4
  timeago: ^3.7.0
  file_picker: ^8.3.7
  csv: ^6.0.0
  excel: ^4.0.6
  path_provider: ^2.1.2
  device_info_plus: ^11.4.0
  drag_and_drop_flutter:
    path: C:/Users/<USER>/Documents/flutter_packages/drag_and_drop_flutter-0.3.0
  graphview: ^1.2.0
  vsc_quill_delta_to_html: ^1.0.5
  flutter_quill_delta_from_html: ^1.5.2
  simple_html_css: ^5.0.0
  # Added missing dependencies for cached_image.dart
  crypto: ^3.0.3
  http: ^1.1.0
  flutter_spinkit: ^5.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  mockito: ^5.4.4
  build_runner: ^2.4.9

dependency_overrides:
  intl: ^0.20.2

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/logo/
    - assets/templates/

  fonts:
    - family: Alata
      fonts:
        - asset: assets/fonts/Alata-Regular.ttf
























  #


