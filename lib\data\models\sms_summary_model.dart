class SmsSummaryModel {
  final bool status;
  final String message;
  final SmsSummaryData? data;

  SmsSummaryModel({
    required this.status,
    required this.message,
    this.data,
  });

  factory SmsSummaryModel.fromJson(Map<String, dynamic> json) {
    return SmsSummaryModel(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? SmsSummaryData.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }
}

class SmsSummaryData {
  final int totalRecipients;
  final int totalCharacters;
  final int totalSms;
  final double totalCharges;
  final double pricePerUnit;
  final double currentUnitsBalance;
  final double afterUnitsBalance;
  final bool shouldTopup;
  final String senderId;

  SmsSummaryData({
    required this.totalRecipients,
    required this.totalCharacters,
    required this.totalSms,
    required this.totalCharges,
    required this.pricePerUnit,
    required this.currentUnitsBalance,
    required this.afterUnitsBalance,
    required this.shouldTopup,
    required this.senderId,
  });

  factory SmsSummaryData.fromJson(Map<String, dynamic> json) {
    return SmsSummaryData(
      totalRecipients: json['total_receipients'] ?? json['total_recipients'] ?? 0,
      totalCharacters: json['total_characters'] ?? 0,
      totalSms: json['total_sms'] ?? 0,
      totalCharges: (json['total_charges'] ?? 0.0).toDouble(),
      pricePerUnit: (json['price_per_unit'] ?? 0.0).toDouble(),
      currentUnitsBalance: (json['current_units_balance'] ?? 0.0).toDouble(),
      afterUnitsBalance: (json['after_units_balance'] ?? 0.0).toDouble(),
      shouldTopup: json['should_topup'] ?? false,
      senderId: json['sender_id'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_recipients': totalRecipients,
      'total_characters': totalCharacters,
      'total_sms': totalSms,
      'total_charges': totalCharges,
      'price_per_unit': pricePerUnit,
      'current_units_balance': currentUnitsBalance,
      'after_units_balance': afterUnitsBalance,
      'should_topup': shouldTopup,
      'sender_id': senderId,
    };
  }
}
