import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/constants/enums.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/core/app/widgets/custom_dropdown.dart';
import '../../controllers/create_organization_controller.dart';

class OrganizationFormFields extends StatelessWidget {
  const OrganizationFormFields({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CreateOrganizationController>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Organization Name
        Obx(() => CustomTextFormField(
          controller: controller.nameController,
          labelText: 'Organization Name *',
          hintText: 'Enter organization name',
          prefixIcon: const Icon(Icons.business),
          errorText: controller.fieldErrors['name'],
          onChanged: (value) => controller.clearFieldError('name'),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Organization name is required';
            }
            return null;
          },
        )),
        const SizedBox(height: 16),

        // Description
        Obx(() => CustomTextFormField(
          controller: controller.descriptionController,
          labelText: 'Description *',
          hintText: 'Enter organization description',
          prefixIcon: const Icon(Icons.description),
          maxLines: 3,
          errorText: controller.fieldErrors['description'],
          onChanged: (value) => controller.clearFieldError('description'),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Description is required';
            }
            return null;
          },
        )),
        const SizedBox(height: 16),

        // Registration Type
        Obx(() => CustomDropdown<String>(
          labelText: 'Registration Type *',
          value: controller.selectedRegistrationType.value.isNotEmpty 
              ? controller.selectedRegistrationType.value 
              : null,
          prefixIcon: const Icon(Icons.category),
          items: controller.registrationTypes
              .map((type) => DropdownMenuItem<String>(
                    value: type.displayName,
                    child: Text(type.displayName),
                  ))
              .toList(),
          onChanged: (value) {
            if (value != null) {
              controller.setRegistrationType(value);
            }
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Registration type is required';
            }
            return null;
          },
        )),
        const SizedBox(height: 16),

        // Email
        Obx(() => CustomTextFormField(
          controller: controller.emailController,
          labelText: 'Email Address *',
          hintText: 'Enter email address',
          prefixIcon: const Icon(Icons.email),
          keyboardType: TextInputType.emailAddress,
          errorText: controller.fieldErrors['email'],
          onChanged: (value) => controller.clearFieldError('email'),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Email is required';
            }
            if (!GetUtils.isEmail(value.trim())) {
              return 'Please enter a valid email address';
            }
            return null;
          },
        )),
        const SizedBox(height: 16),

        // Phone Number
        Obx(() => CustomTextFormField(
          controller: controller.phoneController,
          labelText: 'Phone Number *',
          hintText: 'Enter phone number',
          prefixIcon: const Icon(Icons.phone),
          keyboardType: TextInputType.phone,
          errorText: controller.fieldErrors['phoneNumber'],
          onChanged: (value) => controller.clearFieldError('phoneNumber'),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Phone number is required';
            }
            return null;
          },
        )),
        const SizedBox(height: 16),

        // Business Number (Optional)
        CustomTextFormField(
          controller: controller.businessNumberController,
          labelText: 'Business Number',
          hintText: 'Enter business registration number',
          prefixIcon: const Icon(Icons.business_center),
        ),
        const SizedBox(height: 16),

        // KRA PIN (Optional)
        CustomTextFormField(
          controller: controller.kraPinController,
          labelText: 'KRA PIN',
          hintText: 'Enter KRA PIN number',
          prefixIcon: const Icon(Icons.pin),
        ),
        const SizedBox(height: 16),

        // Slogan
        Obx(() => CustomTextFormField(
          controller: controller.sloganController,
          labelText: 'Slogan *',
          hintText: 'Enter organization slogan',
          prefixIcon: const Icon(Icons.format_quote),
          errorText: controller.fieldErrors['slogan'],
          onChanged: (value) => controller.clearFieldError('slogan'),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Slogan is required';
            }
            return null;
          },
        )),
        const SizedBox(height: 16),

        // Referral Code (Optional)
        CustomTextFormField(
          controller: controller.referralCodeController,
          labelText: 'Referral Code',
          hintText: 'Enter referral code (optional)',
          prefixIcon: const Icon(Icons.code),
        ),
      ],
    );
  }
}
