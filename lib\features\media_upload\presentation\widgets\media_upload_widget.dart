import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:drag_and_drop_flutter/drag_and_drop_flutter.dart';
import 'package:logger/logger.dart';
import '../../../../core/app/constants/routes.dart';
import '../../controller/media_controller.dart';
import '../../models/media_model.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';

class MediaUploadWidget extends StatefulWidget {
  final bool multipleSelect;
  final Function(List<MediaModel>) onMediaSelected;
  final double width;
  final double height;
  final String category;

  const MediaUploadWidget({
    super.key,
    this.multipleSelect = false,
    required this.onMediaSelected,
    this.width = 100,
    this.height = 100,
    required this.category,
  });

  @override
  State<MediaUploadWidget> createState() => _MediaUploadWidgetState();
}

class _MediaUploadWidgetState extends State<MediaUploadWidget> {
  final List<MediaModel> _selectedMedia = [];
  late MediaController _mediaController;
  bool _isDragging = false;
  final Logger _logger = Get.find<Logger>();

  @override
  void initState() {
    super.initState();
    // Initialize the MediaController if it's not already registered
    _mediaController =
        Get.isRegistered<MediaController>()
            ? Get.find<MediaController>()
            : Get.put(MediaController());
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Ensure we have valid dimensions
        final width = widget.width > 0 ? widget.width : 200.0;
        final height = widget.height > 0 ? widget.height : 150.0;

        return SizedBox(
          width: width,
          height: height,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child:
                    _selectedMedia.isEmpty
                        ? _buildMediaSelector()
                        : _buildSelectedMediaPreview(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMediaSelector() {
    return DragDropArea(
      onDrop: _handleDrop,
      onDragEnter: (_) => setState(() => _isDragging = true),
      onDragExit: () => setState(() => _isDragging = false),
      child: GestureDetector(
        onTap: () => _openMediaSelector(),
        child: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            color:
                _isDragging
                    ? Theme.of(context).primaryColor.withOpacity(0.1)
                    : Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color:
                  _isDragging
                      ? Theme.of(context).primaryColor
                      : Colors.grey[400]!,
              width: _isDragging ? 2.0 : 1.0,
            ),
            boxShadow:
                _isDragging
                    ? [
                      BoxShadow(
                        color: Theme.of(context).primaryColor.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                    : null,
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _isDragging ? IconlyBold.upload : Icons.add_photo_alternate,
                  size: widget.width > 100 ? 32 : 24,
                  color:
                      _isDragging
                          ? Theme.of(context).primaryColor
                          : Colors.grey[600],
                ),
                SizedBox(height: widget.width > 100 ? 8 : 4),
                Text(
                  _isDragging
                      ? 'Drop files here'
                      : widget.width > 100
                      ? 'Select or Drop Media'
                      : 'Select Media',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: widget.width > 100 ? 14 : 12,
                    color:
                        _isDragging
                            ? Theme.of(context).primaryColor
                            : Colors.grey[600],
                    fontWeight:
                        _isDragging ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                if (widget.width > 100 && !_isDragging)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      'Drag & drop supported',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 10, color: Colors.grey[500]),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedMediaPreview() {
    return GestureDetector(
      onTap: () => _openMediaSelector(),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[400]!),
        ),
        child:
            widget.multipleSelect && _selectedMedia.length > 1
                ? _buildMultipleMediaPreview()
                : _buildSingleMediaPreview(),
      ),
    );
  }

  Widget _buildSingleMediaPreview() {
    if (_selectedMedia.isEmpty) {
      return const Center(child: Icon(Icons.image_not_supported, size: 40));
    }

    final media = _selectedMedia.first;
    return Stack(
      children: [
        if (media.mediaUrl != null)
          ClipRRect(
            borderRadius: BorderRadius.circular(7),
            child: Image.network(
              // Fix URL formatting issue by removing any trailing comma
              media.mediaUrl!.replaceAll(',', ''),
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return const Center(
                  child: CircleLoadingAnimation(strokeWidth: 2),
                );
              },
              errorBuilder:
                  (context, error, stackTrace) =>
                      const Center(child: Icon(Icons.broken_image, size: 40)),
            ),
          )
        else
          const Center(child: Icon(Icons.image_not_supported, size: 40)),
        Positioned(
          top: 4,
          right: 4,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: const Icon(Icons.edit, color: Colors.white, size: 16),
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
              onPressed: () => _openMediaSelector(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMultipleMediaPreview() {
    return Stack(
      children: [
        // Use LayoutBuilder to ensure proper constraints
        LayoutBuilder(
          builder: (context, constraints) {
            // Ensure we have valid constraints
            if (constraints.maxWidth <= 0 || constraints.maxHeight <= 0) {
              return const Center(
                child: Icon(Icons.image_not_supported, size: 40),
              );
            }

            return GridView.count(
              crossAxisCount: 2,
              padding: const EdgeInsets.all(4),
              mainAxisSpacing: 4,
              crossAxisSpacing: 4,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children:
                  _selectedMedia.take(4).map((media) {
                    final isOverflow =
                        _selectedMedia.length > 4 && media == _selectedMedia[3];
                    return _buildGridItem(media, isOverflow);
                  }).toList(),
            );
          },
        ),
        Positioned(
          top: 4,
          right: 4,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: const Icon(Icons.edit, color: Colors.white, size: 16),
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
              onPressed: () => _openMediaSelector(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGridItem(MediaModel media, bool isOverflow) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child:
              media.mediaUrl != null
                  ? Image.network(
                    // Fix URL formatting issue by removing any trailing comma
                    media.mediaUrl!.replaceAll(',', ''),
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.cover,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return const Center(
                        child: CircleLoadingAnimation(strokeWidth: 2),
                      );
                    },
                    errorBuilder:
                        (context, error, stackTrace) => const Center(
                          child: Icon(Icons.broken_image, size: 20),
                        ),
                  )
                  : const Center(
                    child: Icon(Icons.image_not_supported, size: 20),
                  ),
        ),
        if (isOverflow)
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              borderRadius: BorderRadius.circular(4),
            ),
            alignment: Alignment.center,
            child: Text(
              '+${_selectedMedia.length - 3}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
      ],
    );
  }

  void _openMediaSelector() {
    // Clear any previous selections in the controller
    _mediaController.clearSelection();

    // Pre-select any media that's already selected in this widget
    for (var media in _selectedMedia) {
      _mediaController.toggleMediaSelection(media, widget.multipleSelect);
    }

    // Set the category in the controller for file path formatting
    // This will be used to structure the file path as /<organisation_id>/<CATEGORY>/<type>
    _mediaController.selectedCategory.value = widget.category;

    // Call the showMediaOptions method directly to skip the intermediate dialog
    _showMediaOptionsWithCallback(context);
  }

  /// Enhanced drag and drop handler for the main widget
  Future<void> _handleDrop(DragData data) async {
    setState(() => _isDragging = false);

    try {
      _logger.d(
        'MediaUploadWidget: Processing ${data.items.length} dropped items',
      );

      if (data.items.isEmpty) {
        _logger.w('MediaUploadWidget: No items found in drop data');
        return;
      }

      // Set the category for proper filename formatting
      _mediaController.selectedCategory.value = widget.category;

      // Show loading indicator
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => const AlertDialog(
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircleLoadingAnimation(),
                    SizedBox(height: 16),
                    Text('Processing dropped files...'),
                  ],
                ),
              ),
        );
      }

      final List<XFile> validFiles = [];

      // Process each dropped item
      for (final item in data.items) {
        try {
          _logger.d(
            'MediaUploadWidget: Processing item with type: ${item.type}',
          );

          if (item.data != null) {
            // Generate filename based on MIME type
            String fileName = 'unknown_file';
            if (item.type.isNotEmpty) {
              final extension = _getExtensionFromMimeType(item.type);
              final timestamp = DateTime.now().millisecondsSinceEpoch;

              if (item.type.startsWith('image/')) {
                fileName = 'dropped_image_$timestamp.$extension';
              } else if (item.type.startsWith('video/')) {
                fileName = 'dropped_video_$timestamp.$extension';
              } else if (item.type.startsWith('audio/')) {
                fileName = 'dropped_audio_$timestamp.$extension';
              } else {
                fileName = 'dropped_file_$timestamp.$extension';
              }

              // Remove all white spaces from filename
              fileName = fileName
                  .replaceAll(' ', '')
                  .replaceAll(RegExp(r'\s+'), '');

              _logger.d('MediaUploadWidget: Generated filename: $fileName');
              _logger.d('MediaUploadWidget: MIME type: ${item.type}');
              _logger.d('MediaUploadWidget: File extension: $extension');
              _logger.d('MediaUploadWidget: Timestamp: $timestamp');
              _logger.d(
                'MediaUploadWidget: Filename after whitespace removal: $fileName',
              );
            }

            final fileExtension = fileName.split('.').last.toLowerCase();

            // Validate file type
            if (_isValidFileType(fileExtension)) {
              try {
                // Handle different data types from drag and drop
                Uint8List? fileBytes;

                if (item.data is String) {
                  // Try to decode if it's base64 data
                  final dataString = item.data as String;
                  if (dataString.startsWith('data:')) {
                    // Handle data URL format: data:image/jpeg;base64,/9j/4AAQ...
                    try {
                      final base64Data = dataString.split(',').last;
                      fileBytes = base64Decode(base64Data);
                      _logger.d('MediaUploadWidget: Decoded base64 data URL');
                    } catch (e) {
                      _logger.e(
                        'MediaUploadWidget: Failed to decode base64 data URL: $e',
                      );
                      continue;
                    }
                  } else {
                    // Try direct base64 decode
                    try {
                      fileBytes = base64Decode(dataString);
                      _logger.d('MediaUploadWidget: Decoded base64 string');
                    } catch (e) {
                      _logger.w(
                        'MediaUploadWidget: String data is not base64, skipping: $e',
                      );
                      continue;
                    }
                  }
                } else if (item.data is Uint8List) {
                  // Direct binary data
                  fileBytes = item.data as Uint8List;
                  _logger.d('MediaUploadWidget: Using direct binary data');
                } else {
                  _logger.w(
                    'MediaUploadWidget: Unsupported data type: ${item.data.runtimeType}',
                  );
                  continue;
                }

                if (fileBytes.isNotEmpty) {
                  // Create XFile from binary data
                  final xFile = XFile.fromData(
                    fileBytes,
                    name: fileName,
                    mimeType: item.type,
                  );
                  validFiles.add(xFile);
                  _logger.d(
                    'MediaUploadWidget: Added valid file: $fileName (${fileBytes.length} bytes)',
                  );
                } else {
                  _logger.w('MediaUploadWidget: No valid file data found');
                  continue;
                }
              } catch (e) {
                _logger.e('MediaUploadWidget: Error creating XFile: $e');
                continue;
              }
            } else {
              _logger.w(
                'MediaUploadWidget: Skipped invalid file type: $fileName',
              );
            }
          }
        } catch (e) {
          _logger.e('MediaUploadWidget: Error processing item: $e');
        }
      }

      // Upload valid files
      if (validFiles.isNotEmpty) {
        _logger.d(
          'MediaUploadWidget: Uploading ${validFiles.length} valid files',
        );

        for (final file in validFiles) {
          await _mediaController.uploadSingleMedia(file);
        }

        // Refresh media list
        await _mediaController.fetchMedia(refresh: true);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Successfully uploaded ${validFiles.length} file(s)',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        _logger.w('MediaUploadWidget: No valid files found');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No valid media files found in drop'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }

      // Close loading dialog
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
    } catch (e) {
      _logger.e('MediaUploadWidget: Error handling drop: $e');

      // Close loading dialog if open
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error processing dropped files: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showMediaOptionsWithCallback(BuildContext context) {
    final MediaController mediaController =
        Get.isRegistered<MediaController>()
            ? Get.find<MediaController>()
            : Get.put(MediaController());

    mediaController.fetchMedia(refresh: true);

    final bool isSmallScreen = MediaQuery.of(context).size.width < 600;

    // Store the parent context to use for navigation after dialog/bottom sheet is closed
    final BuildContext parentContext = context;

    if (isSmallScreen) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder:
            (context) => Container(
              height: MediaQuery.of(context).size.height * 0.8,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Select Media',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(color: Theme.of(context).primaryColor),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ],
                    ),
                  ),
                  const Divider(height: 1),

                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Upload Media',
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(
                            color: Theme.of(
                              context,
                            ).primaryColor.withOpacity(0.8),
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildUploadOption(
                          context,
                          icon: IconlyBold.image,
                          title: 'Choose from Gallery',
                          onTap:
                              () => _pickImage(
                                ImageSource.gallery,
                                mediaController,
                                widget.category,
                              ),
                        ),
                        const SizedBox(height: 16),
                        _buildUploadOption(
                          context,
                          icon: IconlyBold.camera,
                          title: 'Take a Photo',
                          onTap:
                              () => _pickImage(
                                ImageSource.camera,
                                mediaController,
                                widget.category,
                              ),
                        ),
                        const SizedBox(height: 16),
                        Container(
                          height: 120,
                          decoration: BoxDecoration(
                            color: Theme.of(
                              context,
                            ).primaryColor.withOpacity(0.05),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Theme.of(
                                context,
                              ).primaryColor.withOpacity(0.3),
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.2),
                                spreadRadius: 1,
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: _buildDragDropArea(context, mediaController),
                        ),
                      ],
                    ),
                  ),
                  const Divider(height: 1),

                  Expanded(
                    child: _buildMediaGallery(
                      context,
                      mediaController,
                      parentContext,
                    ),
                  ),

                  const Divider(height: 1),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        CustomButton(
                          onPressed: () => Navigator.pop(context),

                          icon: const Icon(Icons.close),
                          label: const Text('Cancel'),
                        ),
                        const SizedBox(width: 8),
                        CustomButton(
                          onPressed: () {
                            if (mediaController.selectedMedia.isNotEmpty) {
                              Navigator.pop(context);
                              _navigateToConfirmationScreen(
                                parentContext,
                                mediaController.selectedMedia,
                              );
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'Please select at least one media item',
                                  ),
                                ),
                              );
                            }
                          },

                          icon: const Icon(IconlyLight.tickSquare),
                          label: const Text('Select'),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
      );
    } else {
      showDialog(
        context: context,
        builder:
            (context) => Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Container(
                height: MediaQuery.of(context).size.height * 0.7,
                width: MediaQuery.of(context).size.width * 0.8,
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Select Media',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(color: Theme.of(context).primaryColor),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ],
                    ),
                    const Divider(height: 1),

                    Expanded(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: 250,
                            padding: const EdgeInsets.only(
                              right: 16.0,
                              top: 16.0,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Upload Media',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.titleMedium?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).primaryColor.withOpacity(0.8),
                                  ),
                                ),
                                const SizedBox(height: 16),
                                _buildUploadOption(
                                  context,
                                  icon: Icons.photo_library,
                                  title: 'Choose from Gallery',
                                  onTap:
                                      () => _pickImage(
                                        ImageSource.gallery,
                                        mediaController,
                                        widget.category,
                                      ),
                                ),
                                const SizedBox(height: 16),
                                _buildUploadOption(
                                  context,
                                  icon: Icons.camera_alt,
                                  title: 'Take a Photo',
                                  onTap:
                                      () => _pickImage(
                                        ImageSource.camera,
                                        mediaController,
                                        widget.category,
                                      ),
                                ),
                                const SizedBox(height: 16),
                                Container(
                                  height: 150,
                                  decoration: BoxDecoration(
                                    color: Theme.of(
                                      context,
                                    ).primaryColor.withOpacity(0.05),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Theme.of(
                                        context,
                                      ).primaryColor.withOpacity(0.3),
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.grey.withOpacity(0.2),
                                        spreadRadius: 1,
                                        blurRadius: 2,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                  child: _buildDragDropArea(
                                    context,
                                    mediaController,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const VerticalDivider(width: 1),

                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.only(
                                left: 16.0,
                                top: 16.0,
                              ),
                              child: _buildMediaGallery(
                                context,
                                mediaController,
                                parentContext,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const Divider(height: 1),
                    Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          CustomButton(
                            onPressed: () => Navigator.pop(context),

                            icon: const Icon(Icons.close),
                            label: const Text('Cancel'),
                          ),
                          const SizedBox(width: 8),
                          CustomButton(
                            onPressed: () {
                              if (mediaController.selectedMedia.isNotEmpty) {
                                Navigator.pop(context);
                                _navigateToConfirmationScreen(
                                  parentContext,
                                  mediaController.selectedMedia,
                                );
                              } else {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'Please select at least one media item',
                                    ),
                                  ),
                                );
                              }
                            },

                            icon: const Icon(IconlyLight.tickSquare),
                            label: const Text('Select'),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
      );
    }
  }

  Widget _buildUploadOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return CustomButton(
      onPressed: onTap,

      icon: Icon(icon),
      label: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
    );
  }

  Widget _buildDragDropArea(BuildContext context, MediaController controller) {
    return Stack(
      alignment: Alignment.center,
      children: [
        if (kIsWeb)
          DragTarget<List<dynamic>>(
            onAcceptWithDetails: (details) async {
              if (details.data.isNotEmpty) {
                BuildContext? dialogContext;
                try {
                  // Set the category in the controller before uploading
                  controller.selectedCategory.value = widget.category;

                  // Show upload dialog
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (BuildContext context) {
                      dialogContext = context;
                      return const AlertDialog(
                        content: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CircleLoadingAnimation(),
                            SizedBox(height: 16),
                            Text('Uploading dropped files...'),
                          ],
                        ),
                      );
                    },
                  );

                  final files = details.data;
                  for (final file in files) {
                    if (file is XFile) {
                      await controller.uploadSingleMedia(file);
                    }
                  }

                  // Close upload dialog
                  if (dialogContext != null &&
                      Navigator.canPop(dialogContext!)) {
                    Navigator.pop(dialogContext!);
                  }
                } catch (e) {
                  // Close upload dialog if open
                  if (dialogContext != null &&
                      Navigator.canPop(dialogContext!)) {
                    Navigator.pop(dialogContext!);
                  }

                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error processing dropped files: $e'),
                      ),
                    );
                  }
                }
              }
            },
            onWillAccept: (data) => true,
            builder: (context, candidateData, rejectedData) {
              return Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      IconlyBold.upload,
                      size: 40,
                      color: Theme.of(context).primaryColor.withOpacity(0.5),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Drag and drop files here',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor.withOpacity(0.5),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            },
          )
        else
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  IconlyBold.upload,
                  size: 40,
                  color: Theme.of(context).primaryColor.withOpacity(0.5),
                ),
                const SizedBox(height: 8),
                Text(
                  'Upload files from your device',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor.withOpacity(0.5),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

        Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(8),
            onTap:
                () => _pickImage(
                  ImageSource.gallery,
                  controller,
                  widget.category,
                ),
            child: SizedBox(width: double.infinity, height: double.infinity),
          ),
        ),
      ],
    );
  }

  Future<void> _pickImage(
    ImageSource source,
    MediaController controller,
    String category,
  ) async {
    if (controller.isUploading.value) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please wait for the current upload to complete'),
          ),
        );
      }
      return;
    }

    // Set the category in the controller
    controller.selectedCategory.value = category;

    BuildContext? dialogContext;

    try {
      final ImagePicker picker = ImagePicker();
      if (widget.multipleSelect && source == ImageSource.gallery) {
        final List<XFile> images = await picker.pickMultiImage();
        if (images.isNotEmpty) {
          // Show upload dialog and store its context
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              dialogContext = context;
              return const AlertDialog(
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircleLoadingAnimation(),
                    SizedBox(height: 16),
                    Text('Uploading media...'),
                  ],
                ),
              );
            },
          );

          final uploadedMedia = await controller.uploadMultipleMedia(images);

          // Close the upload dialog using its specific context
          if (dialogContext != null && Navigator.canPop(dialogContext!)) {
            Navigator.pop(dialogContext!);
          }

          // For single select mode, automatically select the uploaded media and close modal
          if (!widget.multipleSelect && uploadedMedia.isNotEmpty) {
            // Close the media selection modal
            if (mounted && Navigator.canPop(context)) {
              Navigator.pop(context);
            }
            // Call the callback with uploaded media
            widget.onMediaSelected(uploadedMedia);
          }
          // For multiple select mode, just refresh the gallery so user can select
        }
      } else {
        final XFile? image = await picker.pickImage(source: source);
        if (image != null) {
          // Show upload dialog and store its context
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              dialogContext = context;
              return const AlertDialog(
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircleLoadingAnimation(),
                    SizedBox(height: 16),
                    Text('Uploading media...'),
                  ],
                ),
              );
            },
          );

          final uploadedMedia = await controller.uploadSingleMedia(image);

          // Close the upload dialog using its specific context
          if (dialogContext != null && Navigator.canPop(dialogContext!)) {
            Navigator.pop(dialogContext!);
          }

          // Automatically select the uploaded media and close modal
          if (uploadedMedia != null && mounted) {
            // Close the media selection modal
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }
            // Call the callback with uploaded media
            widget.onMediaSelected([uploadedMedia]);
          }
        }
      }
    } catch (e) {
      // Close the upload dialog if it's open
      if (dialogContext != null && Navigator.canPop(dialogContext!)) {
        Navigator.pop(dialogContext!);
      }

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error picking image: $e')));
      }
    }
  }

  void _navigateToConfirmationScreen(
    BuildContext context,
    List<MediaModel> selectedMedia,
  ) {
    // Ensure the category is set in the controller before navigating
    final MediaController controller = Get.find<MediaController>();
    controller.selectedCategory.value = widget.category;

    if (widget.multipleSelect || selectedMedia.length > 1) {
      context.push(
        Routes.MEDIA_CONFIRMATION,
        extra: {
          'selectedMedia': selectedMedia,
          'onConfirm': (List<MediaModel> confirmedMedia) {
            widget.onMediaSelected(confirmedMedia);
            // Navigation will be handled by the confirmation screen
          },
        },
      );
    } else {
      widget.onMediaSelected(selectedMedia);
    }
  }

  Widget _buildMediaGallery(
    BuildContext context,
    MediaController controller,
    BuildContext parentContext,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Media Gallery',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Theme.of(context).primaryColor.withOpacity(0.8),
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: Obx(() {
            if (controller.isLoading.value) {
              return const Center(child: CircleLoadingAnimation());
            }

            if (controller.mediaItems.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      IconlyBold.image,
                      size: 64,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No media found',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Upload some media to get started',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              );
            }

            return GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                childAspectRatio: 1,
              ),
              itemCount: controller.mediaItems.length,
              itemBuilder: (context, index) {
                final media = controller.mediaItems[index];
                final isSelected = controller.selectedMedia.contains(media);

                return GestureDetector(
                  onTap: () {
                    if (widget.multipleSelect) {
                      controller.toggleMediaSelection(media, true);
                    } else {
                      controller.selectSingleMedia(media);
                      Navigator.pop(context);
                      _navigateToConfirmationScreen(parentContext, [media]);
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color:
                            isSelected
                                ? Theme.of(context).primaryColor
                                : Colors.grey.shade300,
                        width: isSelected ? 3 : 1,
                      ),
                    ),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(6),
                          child: Image.network(
                            media.mediaUrl ?? '',
                            width: double.infinity,
                            height: double.infinity,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey.shade200,
                                child: Icon(
                                  IconlyBold.image,
                                  color: Colors.grey.shade400,
                                ),
                              );
                            },
                          ),
                        ),
                        if (isSelected)
                          Positioned(
                            top: 4,
                            right: 4,
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            );
          }),
        ),
      ],
    );
  }

  /// Check if the file extension is valid for media upload
  bool _isValidFileType(String extension) {
    const validExtensions = {
      // Images
      'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg',
      // Videos
      'mp4', 'mov', 'avi', 'wmv', 'flv', 'webm',
      // Audio
      'mp3', 'wav', 'ogg', 'aac', 'm4a',
      // Documents
      'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt',
    };
    return validExtensions.contains(extension);
  }

  /// Get file extension from MIME type
  String _getExtensionFromMimeType(String mimeType) {
    switch (mimeType) {
      // Images
      case 'image/jpeg':
        return 'jpg';
      case 'image/png':
        return 'png';
      case 'image/gif':
        return 'gif';
      case 'image/webp':
        return 'webp';
      case 'image/bmp':
        return 'bmp';
      case 'image/svg+xml':
        return 'svg';

      // Videos
      case 'video/mp4':
        return 'mp4';
      case 'video/quicktime':
        return 'mov';
      case 'video/x-msvideo':
        return 'avi';
      case 'video/x-ms-wmv':
        return 'wmv';
      case 'video/x-flv':
        return 'flv';
      case 'video/webm':
        return 'webm';

      // Audio
      case 'audio/mpeg':
        return 'mp3';
      case 'audio/wav':
        return 'wav';
      case 'audio/ogg':
        return 'ogg';
      case 'audio/aac':
        return 'aac';
      case 'audio/mp4':
        return 'm4a';

      // Documents
      case 'application/pdf':
        return 'pdf';
      case 'application/msword':
        return 'doc';
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return 'docx';
      case 'application/vnd.ms-excel':
        return 'xls';
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return 'xlsx';
      case 'application/vnd.ms-powerpoint':
        return 'ppt';
      case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        return 'pptx';
      case 'text/plain':
        return 'txt';

      default:
        return 'bin'; // Binary file extension as fallback
    }
  }
}
