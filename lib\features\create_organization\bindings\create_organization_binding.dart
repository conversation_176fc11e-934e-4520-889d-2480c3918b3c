import 'package:get/get.dart';
import '../controllers/create_organization_controller.dart';
import '../services/organization_service.dart';

class CreateOrganizationBinding extends Bindings {
  @override
  void dependencies() {
    // Register the service
    Get.lazyPut<OrganizationService>(() => OrganizationService());
    
    // Register the controller
    Get.lazyPut<CreateOrganizationController>(() => CreateOrganizationController());
  }
}
