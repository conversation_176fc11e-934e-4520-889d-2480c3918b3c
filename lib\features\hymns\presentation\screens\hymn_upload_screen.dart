import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import '../../controllers/hymn_controller.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import 'package:go_router/go_router.dart';
class HymnUploadScreen extends StatefulWidget {
  const HymnUploadScreen({super.key});

  @override
  State<HymnUploadScreen> createState() => _HymnUploadScreenState();
}

class _HymnUploadScreenState extends State<HymnUploadScreen> {
  final controller = Get.find<HymnController>();
  final titleController = TextEditingController();
  final artistController = TextEditingController();
  final albumController = TextEditingController();
  final mediaUrlController = TextEditingController();
  final verses = <TextEditingController>[TextEditingController()];
  final chorus = <TextEditingController>[TextEditingController()];
  final selectedCategories = <String>[];

  @override
  void dispose() {
    titleController.dispose();
    artistController.dispose();
    albumController.dispose();
    mediaUrlController.dispose();
    for (var controller in verses) {
      controller.dispose();
    }
    for (var controller in chorus) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      persistentFooterButtons: [
        Obx(
          () =>
              controller.isLoading.value
                  ? const Center(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.0),
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: CircleLoadingAnimation(strokeWidth: 2),
                      ),
                    ),
                  )
                  : CustomButton(
                    onPressed: _uploadHymn,
                    icon: const Icon(IconlyLight.upload),
                    label: const Text('Upload'),
                  ),
        ),
      ],
      appBar: AppBar(
        title: Text(
          'Upload Hymn',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Basic Info Section
            Text(
              'Basic Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: titleController,
              labelText: 'Title',
              prefixIcon: Icon(IconlyLight.document),
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: artistController,
              labelText: 'Artist',
              prefixIcon: Icon(IconlyLight.profile),
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: albumController,
              labelText: 'Album',
              prefixIcon: Icon(IconlyLight.folder),
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: mediaUrlController,
              labelText: 'Media URL',
              prefixIcon: Icon(IconlyLight.play),
            ),

            const SizedBox(height: 32),

            // Verses Section
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Verses',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.add_circle_outline),
                  onPressed: () {
                    setState(() {
                      verses.add(TextEditingController());
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: verses.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: CustomTextField(
                          controller: verses[index],
                          maxLines: 3,
                          labelText: 'Verse ${index + 1}',
                        ),
                      ),
                      if (verses.length > 1)
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: IconButton(
                            icon: const Icon(Icons.remove_circle_outline),
                            color: colorScheme.error,
                            onPressed: () {
                              setState(() {
                                verses[index].dispose();
                                verses.removeAt(index);
                              });
                            },
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),

            const SizedBox(height: 32),

            // Chorus Section
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Chorus',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.add_circle_outline),
                  onPressed: () {
                    setState(() {
                      chorus.add(TextEditingController());
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: chorus.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: CustomTextField(
                          controller: chorus[index],
                          maxLines: 2,
                          labelText: 'Chorus Line ${index + 1}',
                        ),
                      ),
                      if (chorus.length > 1)
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: IconButton(
                            icon: const Icon(Icons.remove_circle_outline),
                            color: colorScheme.error,
                            onPressed: () {
                              setState(() {
                                chorus[index].dispose();
                                chorus.removeAt(index);
                              });
                            },
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),

            const SizedBox(height: 32),

            // Categories Section
            Text(
              'Categories',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                FilterChip(
                  label: const Text('Worship'),
                  selected: selectedCategories.contains(
                    '0196a547-e145-7f14-80b4-760e34aaa8ed',
                  ),
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        selectedCategories.add(
                          '0196a547-e145-7f14-80b4-760e34aaa8ed',
                        );
                      } else {
                        selectedCategories.remove(
                          '0196a547-e145-7f14-80b4-760e34aaa8ed',
                        );
                      }
                    });
                  },
                ),
                FilterChip(
                  label: const Text('Praise'),
                  selected: selectedCategories.contains(
                    '0196a547-e145-7f31-9897-adffa2151720',
                  ),
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        selectedCategories.add(
                          '0196a547-e145-7f31-9897-adffa2151720',
                        );
                      } else {
                        selectedCategories.remove(
                          '0196a547-e145-7f31-9897-adffa2151720',
                        );
                      }
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Future<void> _uploadHymn() async {
    final result = await controller.createHymn(
      title: titleController.text,
      artist: artistController.text,
      album: albumController.text,
      verses: verses.map((c) => c.text).toList(),
      chorus: chorus.map((c) => c.text).toList(),
      mediaUrl: mediaUrlController.text,
      categoryIds: selectedCategories,
    );

    if (result) {
      context.pop();
      ToastUtils.showSuccessToast('Success', 'Hymn uploaded successfully');
    } else {
      ToastUtils.showErrorToast('Error', controller.errorMessage.value);
    }
  }
}
