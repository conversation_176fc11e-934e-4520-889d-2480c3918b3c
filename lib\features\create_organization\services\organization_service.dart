import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/services/api_urls.dart';
import 'package:onechurch/core/app/services/http_service.dart' as ht;
import '../data/models/organization_model.dart';

class OrganizationService {
  final ht.HttpService _httpService = Get.find();
  final Logger logger = Get.find<Logger>();

  // Initialize the service
  OrganizationService() {
    _httpService.initializeDio();
  }

  /// Create a new organization
  Future<Map<String, dynamic>> createOrganization(OrganizationModel organization) async {
    try {
      logger.d('Creating organization: ${organization.toJson()}');

      final response = await _httpService.request(
        url: ApiUrls.createOrganisation,
        method: ht.Method.POST,
        params: organization.toJson(),
      );

      logger.d('Create organization response: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return {
          'status': true,
          'message': 'Organization created successfully',
          'data': response.data,
        };
      } else {
        return {
          'status': false,
          'message': response.data['message'] ?? 'Failed to create organization',
          'data': null,
        };
      }
    } catch (e) {
      logger.e('Error creating organization: $e');
      return {
        'status': false,
        'message': 'Failed to create organization: $e',
        'data': null,
      };
    }
  }

  /// Get organization by ID
  Future<Map<String, dynamic>> getOrganizationById(String id) async {
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.getOrganisation}$id',
        method: ht.Method.GET,
      );

      if (response.statusCode == 200) {
        return {
          'status': true,
          'message': 'Organization fetched successfully',
          'data': response.data,
        };
      } else {
        return {
          'status': false,
          'message': response.data['message'] ?? 'Failed to fetch organization',
          'data': null,
        };
      }
    } catch (e) {
      logger.e('Error fetching organization: $e');
      return {
        'status': false,
        'message': 'Failed to fetch organization: $e',
        'data': null,
      };
    }
  }

  /// Update organization
  Future<Map<String, dynamic>> updateOrganization(String id, OrganizationModel organization) async {
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.updateOrganisation}$id',
        method: ht.Method.PUT,
        params: organization.toJson(),
      );

      if (response.statusCode == 200) {
        return {
          'status': true,
          'message': 'Organization updated successfully',
          'data': response.data,
        };
      } else {
        return {
          'status': false,
          'message': response.data['message'] ?? 'Failed to update organization',
          'data': null,
        };
      }
    } catch (e) {
      logger.e('Error updating organization: $e');
      return {
        'status': false,
        'message': 'Failed to update organization: $e',
        'data': null,
      };
    }
  }

  /// Validate organization data before submission
  Map<String, String?> validateOrganization(OrganizationModel organization) {
    Map<String, String?> errors = {};

    if (organization.name.trim().isEmpty) {
      errors['name'] = 'Organization name is required';
    }

    if (organization.description.trim().isEmpty) {
      errors['description'] = 'Description is required';
    }

    if (organization.email.trim().isEmpty) {
      errors['email'] = 'Email is required';
    } else if (!GetUtils.isEmail(organization.email)) {
      errors['email'] = 'Please enter a valid email address';
    }

    if (organization.phoneNumber.trim().isEmpty) {
      errors['phoneNumber'] = 'Phone number is required';
    } else if (!GetUtils.isPhoneNumber(organization.phoneNumber)) {
      errors['phoneNumber'] = 'Please enter a valid phone number';
    }

    if (organization.registrationType.trim().isEmpty) {
      errors['registrationType'] = 'Registration type is required';
    }

    if (organization.slogan.trim().isEmpty) {
      errors['slogan'] = 'Slogan is required';
    }

    return errors;
  }
}
