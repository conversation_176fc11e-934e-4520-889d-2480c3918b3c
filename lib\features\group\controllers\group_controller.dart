import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/features/media_upload/models/media_model.dart';
import 'package:onechurch/features/members/controllers/member_controller.dart';
import '../../../data/models/group_model.dart';
import '../../../data/models/member_model.dart';
import '../services/group_service.dart';

class GroupController extends GetxController {
  final GroupService _groupService = GroupService();
  final logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<GroupModel> groups = <GroupModel>[].obs;
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalItems = 0.obs;
  final RxInt pageSize = 10.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;
  final RxString searchQuery = ''.obs;
  final RxString statusFilter = ''.obs;
  final TextEditingController searchController = TextEditingController();
  final RxString memberId = ''.obs;

  // Group detail variables
  final RxBool isLoadingGroupDetail = false.obs;
  final Rx<GroupModel?> selectedGroup = Rx<GroupModel?>(null);
  final RxString groupDetailError = ''.obs;

  // Date filter variables
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  // Form controllers for create/edit
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final RxString statusValue = 'active'.obs;
  final RxList<MediaModel> mediaItems = <MediaModel>[].obs;
  final TextEditingController mediaTitleController = TextEditingController();
  final TextEditingController mediaUrlController = TextEditingController();
  final RxString mediaTypeValue = 'IMAGE'.obs;

  // TODO USE A MODEL CLASS
  final RxList<Map<String, dynamic>> selectedMembers =
      <Map<String, dynamic>>[].obs;
  final RxBool isSubmitting = false.obs;

  // For group selection mode
  final RxList<GroupModel> selectedGroups = <GroupModel>[].obs;

  // Clear selected groups
  void clearSelectedGroups() {
    selectedGroups.clear();
  }

  @override
  void onInit() {
    super.onInit();
    fetchGroups();
  }

  @override
  void onClose() {
    searchController.dispose();
    titleController.dispose();
    descriptionController.dispose();
    mediaTitleController.dispose();
    mediaUrlController.dispose();
    super.onClose();
  }

  // Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    currentPage.value = 0; // Reset to first page when searching
    fetchGroups();
  }

  // Set date filters
  void setDateFilters(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0; // Reset to first page when filtering
    fetchGroups();
  }

  // Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    searchController.clear();
    statusFilter.value = '';
    startDate.value = null;
    endDate.value = null;
    memberId.value = '';
    currentPage.value = 0;
    fetchGroups();
  }

  // Fetch a specific group by ID
  Future<void> fetchGroupById(String id) async {
    isLoadingGroupDetail.value = true;
    groupDetailError.value = '';
    selectedGroup.value = null;

    try {
      final response = await _groupService.getGroupById(id);

      if (response["status"] ?? false) {
        final data = response["data"];
        // Parse the group data
        if (data != null) {
          selectedGroup.value = GroupModel.fromJson(data);
        } else {
          groupDetailError.value = "Group data not found";
        }
      } else {
        groupDetailError.value =
            response["message"] ?? "Failed to fetch group details";
      }
    } catch (e) {
      logger.e('Error in fetchGroupById: $e');
      groupDetailError.value = "An error occurred while fetching group details";
    } finally {
      isLoadingGroupDetail.value = false;
    }
  }

  // Navigate to next page
  void nextPage() {
    if (!isLastPage.value) {
      currentPage.value++;
      fetchGroups();
    }
  }

  // Navigate to previous page
  void previousPage() {
    if (currentPage.value > 0) {
      currentPage.value--;
      fetchGroups();
    }
  }

  // Refresh groups
  Future<void> refreshGroups() async {
    currentPage.value = 0;
    await fetchGroups();
  }

  // Fetch groups with pagination and filters
  Future<void> fetchGroups() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _groupService.fetchGroups(
        page: currentPage.value,
        size: pageSize.value,
        searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
        status: statusFilter.value.isEmpty ? null : statusFilter.value,
        startDate: startDate.value,
        endDate: endDate.value,
        memberId: memberId.value.isEmpty ? null : memberId.value,
      );

      if (response["status"] ?? false) {
        final data = response["data"];

        // Parse pagination data
        currentPage.value = data["page"] ?? 0;
        totalPages.value = data["total_pages"] ?? 0;
        totalItems.value = data["total"] ?? 0;
        isLastPage.value = data["last"] ?? false;
        isFirstPage.value = data["first"] ?? true;

        // Parse groups
        final items = data["items"] as List<dynamic>;
        groups.value = _groupService.parseGroups(items);
        groups.refresh();
      } else {
        errorMessage.value = response["message"] ?? "Failed to fetch groups";
      }
    } catch (e) {
      logger.e('Error in fetchGroups: $e');
      errorMessage.value = "An error occurred while fetching groups";
    } finally {
      isLoading.value = false;
    }
  }

  // Load more groups (for infinite scrolling)
  Future<void> loadMoreGroups() async {
    if (isLoading.value || isLastPage.value) return;

    currentPage.value++;
    isLoading.value = true;

    try {
      final response = await _groupService.fetchGroups(
        page: currentPage.value,
        size: pageSize.value,
        searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
        status: statusFilter.value.isEmpty ? null : statusFilter.value,
        startDate: startDate.value,
        endDate: endDate.value,
        memberId: memberId.value.isEmpty ? null : memberId.value,
      );

      if (response["status"] ?? false) {
        final data = response["data"];

        // Update pagination data
        currentPage.value = data["page"] ?? 0;
        totalPages.value = data["total_pages"] ?? 0;
        totalItems.value = data["total"] ?? 0;
        isLastPage.value = data["last"] ?? false;
        isFirstPage.value = data["first"] ?? true;

        // Parse and add new groups
        final items = data["items"] as List<dynamic>;
        final newGroups = _groupService.parseGroups(items);
        groups.addAll(newGroups);
      } else {
        errorMessage.value =
            response["message"] ?? "Failed to load more groups";
      }
    } catch (e) {
      logger.e('Error in loadMoreGroups: $e');
      errorMessage.value = "An error occurred while loading more groups";
      // Revert page increment on error
      currentPage.value--;
    } finally {
      isLoading.value = false;
    }
  }

  // Clear form fields
  void clearFormFields() {
    titleController.clear();
    descriptionController.clear();
    statusValue.value = 'active';
    mediaTypeValue.value = 'IMAGE';
    mediaItems.clear();
    mediaTitleController.clear();
    mediaUrlController.clear();
    selectedMembers.clear();
  }

  // Remove media item from the list
  void removeMediaItem(int index) {
    if (index >= 0 && index < mediaItems.length) {
      mediaItems.removeAt(index);
    }
  }

  // Add member to the group
  void addMember(String memberId, String role) {
    // Check if member already exists
    final existingIndex = selectedMembers.indexWhere(
      (m) => m['member_id'] == memberId,
    );

    if (existingIndex >= 0) {
      // Update existing member role
      selectedMembers[existingIndex]['role'] = role;
    } else {
      // Add new member
      selectedMembers.add({
        'member_id': memberId,
        'status': 'Active',
        'role': role,
      });
    }
    selectedMembers.refresh();
  }

  // Remove member from the group
  void removeMember(String memberId) {
    selectedMembers.removeWhere((m) => m['member_id'] == memberId);
    selectedMembers.refresh();
  }

  // Create a new group
  Future<bool> createGroup() async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      if (titleController.text.isEmpty || descriptionController.text.isEmpty) {
        errorMessage.value = 'Title and description are required';
        return false;
      }

      await _groupService.createGroup(
        title: titleController.text,
        description: descriptionController.text,
        status: statusValue.value,
        members: selectedMembers,
        media:
            mediaItems.isNotEmpty
                ? mediaItems.map((e) => e.toJson()).toList()
                : null,
      );

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to create group: ${e.toString()}';
      logger.e('Error in createGroup: $e');
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Update an existing group
  Future<bool> updateGroup(String id) async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      if (titleController.text.isEmpty || descriptionController.text.isEmpty) {
        errorMessage.value = 'Title and description are required';
        return false;
      }

      await _groupService.updateGroup(
        id: id,
        title: titleController.text,
        description: descriptionController.text,
        status: statusValue.value,
        members: selectedMembers,
        media:
            mediaItems.isNotEmpty
                ? mediaItems.map((e) => e.toJson()).toList()
                : null,
      );

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to update group: ${e.toString()}';
      logger.e('Error in updateGroup: $e');
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Delete a group
  Future<bool> deleteGroup(String id) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      await _groupService.deleteGroup(id);

      refreshGroups();
      return true;
    } catch (e) {
      errorMessage.value = 'Failed to delete group: ${e.toString()}';
      logger.e('Error in deleteGroup: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Load group data into form for editing
  void loadGroupForEdit(GroupModel group) {
    titleController.text = group.title ?? '';
    descriptionController.text = group.description ?? '';
    statusValue.value = group.status ?? 'active';
    // Clear and load members
    selectedMembers.clear();
    // Also clear and populate member controller
    final memberController = Get.find<MemberController>();
    memberController.selectedMembers.clear();
    if (group.members != null && group.members!.isNotEmpty) {
      for (var member in group.members!) {
        selectedMembers.add({
          'member_id': member.memberId,
          'status': member.status ?? 'Active',
          'role': member.role ?? 'MEMBER',
        });
        // Add to member controller if member data exists
        if (member.member != null) {
          logger.d(
            'GroupController: Adding member ${member.member!.firstName} ${member.member!.secondName}',
          );
          // Convert MemberMember to MemberModel
          final memberModel = MemberModel(
            id: member.member!.id,
            createdAt: member.member!.createdAt,
            updatedAt: member.member!.updatedAt,
            deletedAt: member.member!.deletedAt,
            phoneNumber: member.member!.phoneNumber,
            secondaryNumber: member.member!.secondaryNumber,
            profileUrl: member.member!.profileUrl,
            firstName: member.member!.firstName,
            secondName: member.member!.secondName,
            email: member.member!.email,
            address: member.member!.address,
            userId: member.member!.userId,
            idNumber: member.member!.idNumber,
            balance: member.member!.balance,
            organisationId: member.member!.organisationId,
            verificationStatus: member.member!.verificationStatus,
            status: member.member!.status,
            memberCategoryId: member.member!.memberCategoryId,
            accountNumber: member.member!.internalAccountNumber,
          );
          memberController.selectedMembers.add(memberModel);
        }
      }
    } else {
      logger.d('GroupController: No members found in group');
    }
    // Clear and load media items
    mediaItems.clear();
    if (group.media != null) {
      // Handle media based on its type
      if (group.media is List) {
        for (var item in group.media as List) {
          mediaItems.add(MediaModel.fromJson(item));
        }
      }
    }
  }
}
