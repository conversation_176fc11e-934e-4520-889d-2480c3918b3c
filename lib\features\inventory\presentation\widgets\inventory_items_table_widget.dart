import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../../../core/app/constants/routes.dart';
import '../../../../core/app/utils/show_toast.dart';
import '../../controllers/inventory_item_controller.dart';
import '../../models/inventory_item_model.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';

class InventoryItemsTableWidget extends StatefulWidget {
  const InventoryItemsTableWidget({super.key});

  @override
  State<InventoryItemsTableWidget> createState() =>
      _InventoryItemsTableWidgetState();
}

class _InventoryItemsTableWidgetState extends State<InventoryItemsTableWidget> {
  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  PlutoGridStateManager? stateManager;

  @override
  void initState() {
    super.initState();
    _setColumns();
  }

  void _setColumns() {
    columns = [
      PlutoColumn(
        title: 'Item',
        field: 'item',
        type: PlutoColumnType.text(),
        width: 250,
        enableRowChecked: false,
        renderer: (rendererContext) {
          final item =
              rendererContext.row.cells['item']?.value as InventoryItemModel?;
          if (item == null) return const SizedBox();

          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircleAvatar(
                backgroundColor: const Color(0xFFE0E2E7),
                radius: 15,
                backgroundImage:
                    item.hasMedia && item.firstMediaUrl != null
                        ? NetworkImage(item.firstMediaUrl!)
                        : null,
                child:
                    item.hasMedia && item.firstMediaUrl != null
                        ? null
                        : Icon(
                          IconlyLight.category,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      item.title ?? 'Unknown Item',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (item.barcode != null && item.barcode!.isNotEmpty)
                      Text(
                        'Barcode: ${item.barcode}',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey.shade600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
      PlutoColumn(
        title: 'Category',
        field: 'category',
        type: PlutoColumnType.text(),
        width: 120,
        renderer: (rendererContext) {
          final category = rendererContext.cell.value as String?;
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getCategoryColor(category).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _getCategoryColor(category).withOpacity(0.3),
              ),
            ),
            child: Text(
              category ?? 'Unknown',
              style: TextStyle(
                color: _getCategoryColor(category),
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Unit of Measure',
        field: 'unit_of_measure',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        width: 200,
        renderer: (rendererContext) {
          final description = rendererContext.cell.value as String?;
          return Tooltip(
            message: description ?? 'No description',
            child: Text(
              description ?? 'No description',
              style: const TextStyle(fontSize: 11),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final status = rendererContext.cell.value as String?;
          final isActive = status?.toLowerCase() == 'active';
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color:
                  isActive
                      ? Colors.green.withOpacity(0.1)
                      : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color:
                    isActive
                        ? Colors.green.withOpacity(0.3)
                        : Colors.red.withOpacity(0.3),
              ),
            ),
            child: Text(
              status ?? 'Unknown',
              style: TextStyle(
                color: isActive ? Colors.green : Colors.red,
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Created',
        field: 'created_at',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final createdAt = rendererContext.cell.value as DateTime?;
          if (createdAt == null) return const Text('Unknown');

          return Text(
            DateFormat('MMM dd, yyyy').format(createdAt),
            style: const TextStyle(fontSize: 11),
          );
        },
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: false,
        enableColumnDrag: false,
        enableContextMenu: false,
        renderer: (rendererContext) {
          final item =
              rendererContext.row.cells['item']?.value as InventoryItemModel?;
          if (item == null) return const SizedBox();

          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () => _viewItem(item),
                icon: const Icon(IconlyLight.show, size: 16),
                tooltip: 'View Item',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
              IconButton(
                onPressed: () => _editItem(item),
                icon: const Icon(IconlyLight.edit, size: 16),
                tooltip: 'Edit Item',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
              IconButton(
                onPressed: () => _deleteItem(item),
                icon: const Icon(IconlyLight.delete, size: 16),
                tooltip: 'Delete Item',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
            ],
          );
        },
      ),
    ];
  }

  Color _getCategoryColor(String? category) {
    switch (category?.toLowerCase()) {
      case 'furniture':
        return Colors.brown;
      case 'electronics':
        return Colors.blue;
      case 'office supplies':
        return Colors.green;
      case 'kitchen equipment':
        return Colors.orange;
      case 'cleaning supplies':
        return Colors.purple;
      case 'books & materials':
        return Colors.indigo;
      case 'musical instruments':
        return Colors.pink;
      case 'audio/visual equipment':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  void _setRows() {
    final controller = Get.find<InventoryItemController>();
    rows =
        controller.inventoryItems.map((item) {
          final unitOfMeasure = controller.getUnitOfMeasureById(
            item.unitOfMeasureId ?? '',
          );

          return PlutoRow(
            cells: {
              'item': PlutoCell(value: item),
              'category': PlutoCell(value: item.category ?? 'Unknown'),
              'unit_of_measure': PlutoCell(
                value: unitOfMeasure?.fullDisplay ?? 'Unknown',
              ),
              'description': PlutoCell(value: item.description ?? ''),
              'status': PlutoCell(value: item.status),
              'created_at': PlutoCell(value: item.createdAt),
              'actions': PlutoCell(value: ''),
            },
          );
        }).toList();
  }

  void _viewItem(InventoryItemModel item) {
    // Navigate to view item screen
    context.go(Routes.VIEW_INVENTORY_ITEM.replaceAll(':id', item.id ?? ''));
  }

  void _editItem(InventoryItemModel item) {
    // Navigate to edit item screen
    Get.snackbar(
      'Edit Item',
      'Editing ${item.title}',
      snackPosition: SnackPosition.bottom,
    );
  }

  void _deleteItem(InventoryItemModel item) {
    Get.defaultDialog(
      title: 'Delete Item',
      middleText: 'Are you sure you want to delete "${item.title}"?',
      textConfirm: 'Delete',
      textCancel: 'Cancel',
      confirmTextColor: Colors.white,
      buttonColor: Colors.red,
      onConfirm: () async {
        context.pop();
        final controller = Get.find<InventoryItemController>();
        final success = await controller.deleteInventoryItem(item.id ?? '');

        if (success) {
          ToastUtils.showSuccessToast('Item deleted successfully', null);
        } else {
          ToastUtils.showErrorToast('Failed to delete item', null);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<InventoryItemController>(
      builder: (controller) {
        return Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircleLoadingAnimation());
          }

          if (controller.errorMessage.value.isNotEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    IconlyLight.dangerTriangle,
                    size: 64,
                    color: Colors.red.shade300,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading inventory items',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    controller.errorMessage.value,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  CustomButton(
                    onPressed: () => controller.fetchInventoryItems(),
                    label: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (controller.inventoryItems.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    IconlyLight.category,
                    size: 64,
                    color: Colors.grey.shade300,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No inventory items found',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Start by adding your first inventory item',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            );
          }

          _setRows();

          return Card(
            color: Theme.of(context).secondaryHeaderColor,
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            elevation: 4,
            child: PlutoGrid(
              mode: PlutoGridMode.selectWithOneTap,
              columns: columns,
              rows: rows,
              onLoaded: (PlutoGridOnLoadedEvent event) {
                stateManager = event.stateManager;
                event.stateManager.setShowColumnFilter(true);
                event.stateManager.setSelectingMode(PlutoGridSelectingMode.row);

                Future.microtask(() {
                  stateManager?.resetCurrentState();
                  stateManager?.notifyListeners();
                });

                if (kDebugMode) {
                  debugPrint("Inventory Items Grid loaded");
                }
              },
              onSelected: (event) {
                try {
                  final item = controller.inventoryItems[event.rowIdx ?? 0];
                  _viewItem(item);
                } catch (e) {
                  Logger().e(e);
                }
              },
              configuration: PlutoGridConfiguration(
                enableMoveDownAfterSelecting: true,
                style: PlutoGridStyleConfig(
                  activatedColor: const Color.fromARGB(255, 165, 205, 253),
                  cellTextStyle: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Color.fromARGB(255, 64, 64, 64),
                  ),
                  columnTextStyle: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blueGrey,
                  ),
                  rowHeight: 50,
                ),
                columnSize: const PlutoGridColumnSizeConfig(
                  autoSizeMode: PlutoAutoSizeMode.scale,
                  resizeMode: PlutoResizeMode.normal,
                ),
                columnFilter: const PlutoGridColumnFilterConfig(
                  filters: [...FilterHelper.defaultFilters],
                ),
              ),
            ),
          );
        });
      },
    );
  }
}
