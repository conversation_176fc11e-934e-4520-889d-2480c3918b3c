import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';

class PhoneNumberProcessor extends StatefulWidget {
  final Function(List<String> phoneNumbers) onNumbersProcessed;

  const PhoneNumberProcessor({super.key, required this.onNumbersProcessed});

  static show(
    BuildContext context, {
    required Function(List<String> phoneNumbers) onNumbersProcessed,
  }) {
    if (MediaQuery.of(context).size.width < 600) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        builder:
            (context) => Container(
              height: MediaQuery.of(context).size.height * 0.8,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: PhoneNumberProcessor(
                onNumbersProcessed: onNumbersProcessed,
              ),
            ),
      );
    } else {
      showDialog(
        context: context,
        builder:
            (context) => Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                constraints: const BoxConstraints(maxWidth: 600),
                child: PhoneNumberProcessor(
                  onNumbersProcessed: onNumbersProcessed,
                ),
              ),
            ),
      );
    }
  }

  @override
  State<PhoneNumberProcessor> createState() => _PhoneNumberProcessorState();
}

class _PhoneNumberProcessorState extends State<PhoneNumberProcessor> {
  final TextEditingController _controller = TextEditingController();
  final Set<String> _uniqueNumbers =
      {}; // Using Set for automatic duplicate checking
  List<String> processedNumbers = [];
  String? error;
  int duplicatesFound = 0;

  String formatPhoneNumber(String number) {
    // Remove any whitespace and special characters
    number = number.trim().replaceAll(RegExp(r'[^0-9+]'), '');

    if (number.startsWith('+254')) {
      return number;
    } else if ((number.startsWith('07') || number.startsWith('01')) &&
        number.length == 10) {
      return '+254${number.substring(1)}';
    } else if ((number.startsWith('7') || number.startsWith('1')) &&
        number.length == 9) {
      return '+254$number';
    }
    return ''; // Invalid number
  }

  void processNumbers() {
    setState(() {
      error = null;
      _uniqueNumbers.clear();
      duplicatesFound = 0;

      // Split input by common separators (comma, semicolon, newline, space)
      final numbers = _controller.text.split(RegExp(r'[,;\n\s]+'));

      for (String number in numbers) {
        if (number.trim().isEmpty) continue;

        final formattedNumber = formatPhoneNumber(number);
        if (formattedNumber.isNotEmpty) {
          if (!_uniqueNumbers.add(formattedNumber)) {
            // Returns false if number already exists
            duplicatesFound++;
          }
        }
      }

      processedNumbers = _uniqueNumbers.toList();

      if (processedNumbers.isEmpty) {
        error = 'No valid phone numbers found';
      }
    });
  }

  void _handleDone() {
    if (processedNumbers.isNotEmpty) {
      widget.onNumbersProcessed(processedNumbers);
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SelectableText(
            'Paste Phone Numbers',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          TextButton.icon(
            icon: const Icon(Icons.paste),
            label: const Text('Paste'),
            onPressed: () async {
              await Clipboard.getData(Clipboard.kTextPlain).then(
                (value) =>
                    _controller.text =
                        _controller.text + (value?.text?.toString() ?? ''),
              );
            },
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _controller,
            maxLines: 8,
            hintText:
                'Paste phone numbers here...\n'
                'Separate by commas, semicolons, spaces, or new lines\n'
                'Supported formats:\n'
                '- 07xxxxxxxx or 01xxxxxxxx\n'
                '- 7xxxxxxxx or 1xxxxxxxx\n'
                '- +254xxxxxxxxx',
          ),
          const SizedBox(height: 16),
          CustomButton(
            onPressed: processNumbers,
            label: const Text('Process Numbers'),
          ),
          if (error != null) ...[
            const SizedBox(height: 16),
            SelectableText(
              error!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
          ],
          if (processedNumbers.isNotEmpty) ...[
            const SizedBox(height: 16),
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: SingleChildScrollView(
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children:
                            processedNumbers
                                .map(
                                  (number) => Chip(
                                    backgroundColor: Colors.grey.shade100,
                                    label: SelectableText(
                                      number,
                                      style: const TextStyle(fontSize: 14),
                                    ),
                                    deleteIcon: const Icon(
                                      Icons.close,
                                      size: 18,
                                    ),
                                    onDeleted: () {
                                      setState(() {
                                        _uniqueNumbers.remove(number);
                                        processedNumbers =
                                            _uniqueNumbers.toList();
                                        if (processedNumbers.isEmpty) {
                                          error =
                                              'No valid phone numbers found';
                                        }
                                      });
                                    },
                                  ),
                                )
                                .toList(),
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            Column(
              children: [
                if (duplicatesFound > 0)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: SelectableText(
                      'Removed $duplicatesFound duplicate number${duplicatesFound == 1 ? '' : 's'}',
                      style: TextStyle(
                        color: Colors.orange.shade800,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    SelectableText(
                      '${processedNumbers.length} valid number${processedNumbers.length == 1 ? '' : 's'} found',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    ElevatedButton(
                      onPressed: _handleDone,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 12,
                        ),
                        elevation: 0,
                      ),
                      child: const Text('Done'),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
