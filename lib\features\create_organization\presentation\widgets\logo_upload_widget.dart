import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/features/media_upload/media_upload.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import '../../controllers/create_organization_controller.dart';

class LogoUploadWidget extends StatelessWidget {
  const LogoUploadWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CreateOrganizationController>();
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      elevation: 0,
      color: colorScheme.surfaceVariant.withOpacity(0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: colorScheme.outline.withOpacity(0.5)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(IconlyLight.image, color: colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Organization Logo (Optional)',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Upload your organization logo',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),
            MediaUploadWidget(
              category: 'ORGANIZATION',
              multipleSelect: false,
              onMediaSelected: controller.setLogoMedia,
              width: double.infinity,
              height: 120,
            ),
            const SizedBox(height: 12),
            Obx(
              () =>
                  controller.logoMedia.isEmpty
                      ? Center(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Text(
                            'No logo uploaded yet',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface.withOpacity(0.6),
                            ),
                          ),
                        ),
                      )
                      : _buildLogoPreview(
                        context,
                        controller,
                        theme,
                        colorScheme,
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoPreview(
    BuildContext context,
    CreateOrganizationController controller,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final logoItem = controller.logoMedia.first;

    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: Image.network(
          logoItem.mediaUrl ?? '',
          width: 40,
          height: 40,
          fit: BoxFit.cover,
          errorBuilder:
              (context, error, stackTrace) => Container(
                width: 40,
                height: 40,
                color: colorScheme.surfaceVariant,
                child: Icon(Icons.error, size: 20, color: colorScheme.error),
              ),
        ),
      ),
      title: Text(
        logoItem.title ?? 'Organization Logo',
        style: theme.textTheme.bodyMedium,
      ),
      subtitle: Text(
        logoItem.mediaUrl ?? '',
        overflow: TextOverflow.ellipsis,
        style: theme.textTheme.bodySmall?.copyWith(
          color: colorScheme.onSurface.withOpacity(0.6),
        ),
      ),
      trailing: IconButton(
        icon: Icon(Icons.delete, color: colorScheme.error, size: 20),
        onPressed: () => controller.clearLogo(),
        tooltip: 'Remove',
      ),
    );
  }
}
