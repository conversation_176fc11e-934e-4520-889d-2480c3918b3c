import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/constants/enums.dart';
import 'package:onechurch/core/app/constants/routes.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/features/media_upload/media_upload.dart';
import '../data/models/organization_model.dart';
import '../services/organization_service.dart';

class CreateOrganizationController extends GetxController {
  final OrganizationService _organizationService = OrganizationService();
  final Logger logger = Get.find<Logger>();

  // Form controllers
  final nameController = TextEditingController();
  final descriptionController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final businessNumberController = TextEditingController();
  final kraPinController = TextEditingController();
  final referralCodeController = TextEditingController();
  final sloganController = TextEditingController();

  // Form key for validation
  final formKey = GlobalKey<FormState>();

  // Reactive variables
  final RxBool isLoading = false.obs;
  final RxString selectedRegistrationType = ''.obs;
  final RxString logoUrl = ''.obs;
  final RxList<MediaModel> logoMedia = <MediaModel>[].obs;
  final RxMap<String, String?> fieldErrors = <String, String?>{}.obs;

  // Settings reactive variables
  final Rx<Color?> primaryColor = Rx<Color?>(null);
  final Rx<Color?> secondaryColor = Rx<Color?>(null);
  final Rx<Color?> accentColor = Rx<Color?>(null);
  final Rx<Color?> backgroundColor = Rx<Color?>(null);
  final RxMap<String, String?> settingsErrors = <String, String?>{}.obs;

  // Registration type options from enums
  final List<BusinessType> registrationTypes = [
    BusinessType.church,
    BusinessType.religiousBasedInstitution,
    BusinessType.nonGovernmentalOrganization,
    BusinessType.communityBasedOrganization,
    BusinessType.limitedCompany,
    BusinessType.soleProprietorship,
    BusinessType.individual,
  ];

  @override
  void onInit() {
    super.onInit();
    // Set default registration type
    selectedRegistrationType.value = BusinessType.church.displayName;
    // Set default colors
    _setDefaultColors();
  }

  /// Set default theme colors
  void _setDefaultColors() {
    primaryColor.value = const Color(0xFFD32F2F); // Red
    secondaryColor.value = const Color(0xFF2A2A2A); // Dark grey
    accentColor.value = const Color(0xFF4285F4); // Blue
    backgroundColor.value = Colors.white;
  }

  @override
  void onClose() {
    // Dispose controllers
    nameController.dispose();
    descriptionController.dispose();
    emailController.dispose();
    phoneController.dispose();
    businessNumberController.dispose();
    kraPinController.dispose();
    referralCodeController.dispose();
    sloganController.dispose();
    super.onClose();
  }

  /// Set logo URL from media upload
  void setLogoUrl(String url) {
    logoUrl.value = url;
  }

  /// Set logo media from media upload widget
  void setLogoMedia(List<MediaModel> media) {
    logoMedia.value = media;
    if (media.isNotEmpty) {
      logoUrl.value = media.first.mediaUrl ?? '';
    } else {
      logoUrl.value = '';
    }
  }

  /// Clear logo
  void clearLogo() {
    logoUrl.value = '';
    logoMedia.clear();
  }

  /// Set registration type
  void setRegistrationType(String type) {
    selectedRegistrationType.value = type;
    fieldErrors.remove('registrationType');
  }

  /// Clear field error
  void clearFieldError(String field) {
    fieldErrors.remove(field);
  }

  /// Set primary color
  void setPrimaryColor(Color color) {
    primaryColor.value = color;
    settingsErrors.remove('primaryColor');
  }

  /// Set secondary color
  void setSecondaryColor(Color color) {
    secondaryColor.value = color;
    settingsErrors.remove('secondaryColor');
  }

  /// Set accent color
  void setAccentColor(Color color) {
    accentColor.value = color;
    settingsErrors.remove('accentColor');
  }

  /// Set background color
  void setBackgroundColor(Color color) {
    backgroundColor.value = color;
    settingsErrors.remove('backgroundColor');
  }

  /// Reset settings to defaults
  void resetSettingsToDefaults() {
    _setDefaultColors();
    settingsErrors.clear();
  }

  /// Get settings as Map
  Map<String, dynamic> getSettingsMap() {
    return {
      'primaryColor': primaryColor.value?.value.toRadixString(16),
      'secondaryColor': secondaryColor.value?.value.toRadixString(16),
      'accentColor': accentColor.value?.value.toRadixString(16),
      'backgroundColor': backgroundColor.value?.value.toRadixString(16),
    };
  }

  /// Validate form fields
  bool _validateForm() {
    fieldErrors.clear();

    if (nameController.text.trim().isEmpty) {
      fieldErrors['name'] = 'Organization name is required';
    }

    if (descriptionController.text.trim().isEmpty) {
      fieldErrors['description'] = 'Description is required';
    }

    if (emailController.text.trim().isEmpty) {
      fieldErrors['email'] = 'Email is required';
    } else if (!GetUtils.isEmail(emailController.text.trim())) {
      fieldErrors['email'] = 'Please enter a valid email address';
    }

    if (phoneController.text.trim().isEmpty) {
      fieldErrors['phoneNumber'] = 'Phone number is required';
    }

    if (selectedRegistrationType.value.isEmpty) {
      fieldErrors['registrationType'] = 'Registration type is required';
    }

    if (sloganController.text.trim().isEmpty) {
      fieldErrors['slogan'] = 'Slogan is required';
    }

    return fieldErrors.isEmpty;
  }

  /// Create organization
  Future<void> createOrganization() async {
    if (!_validateForm()) {
      ToastUtils.showErrorToast(
        'Validation Error',
        'Please fix the errors and try again',
      );
      return;
    }

    isLoading.value = true;

    try {
      final organization = OrganizationModel(
        name: nameController.text.trim(),
        description: descriptionController.text.trim(),
        registrationType: selectedRegistrationType.value,
        email: emailController.text.trim(),
        phoneNumber: phoneController.text.trim(),
        businessNumber:
            businessNumberController.text.trim().isNotEmpty
                ? businessNumberController.text.trim()
                : null,
        kraPin:
            kraPinController.text.trim().isNotEmpty
                ? kraPinController.text.trim()
                : null,
        logo: logoUrl.value.isNotEmpty ? logoUrl.value : null,
        referralCode:
            referralCodeController.text.trim().isNotEmpty
                ? referralCodeController.text.trim()
                : null,
        slogan: sloganController.text.trim(),
        location: [], // Empty location array as per API example
        settings: getSettingsMap(), // Include organization settings
      );

      logger.d('Creating organization with data: ${organization.toJson()}');

      final result = await _organizationService.createOrganization(
        organization,
      );

      if (result['status'] == true) {
        ToastUtils.showSuccessToast(
          'Success',
          'Organization created successfully',
        );
        final organizationData = result['data'];
        final organizationId = organizationData?['id']?.toString() ?? 'unknown';
        final organizationName = nameController.text.trim();

        _clearForm();

        // Navigate to waiting screen
        Get.context?.push(
          Routes.ORGANIZATION_REVIEW_WAITING,
          extra: {
            'organizationName': organizationName,
            'organizationId': organizationId,
          },
        );
      } else {
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to create organization',
        );
      }
    } catch (e) {
      logger.e('Error creating organization: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
    } finally {
      isLoading.value = false;
    }
  }

  /// Clear form data
  void _clearForm() {
    nameController.clear();
    descriptionController.clear();
    emailController.clear();
    phoneController.clear();
    businessNumberController.clear();
    kraPinController.clear();
    referralCodeController.clear();
    sloganController.clear();
    selectedRegistrationType.value = BusinessType.church.displayName;
    logoUrl.value = '';
    logoMedia.clear();
    fieldErrors.clear();
    settingsErrors.clear();
    _setDefaultColors();
  }

  /// Reset form
  void resetForm() {
    _clearForm();
  }
}
